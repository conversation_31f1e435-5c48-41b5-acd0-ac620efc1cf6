2025-06-27 11:40:13.999 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 11:40:13.999 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 11:40:14.000 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 11:40:14.000 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 11:40:14.001 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 11:40:14.003 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 11:40:14.860 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 11:40:14.869 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:311 | 字段映射保存成功: salary_data_2025_01_retired_employees
2025-06-27 11:40:14.870 | INFO     | src.modules.data_import.config_sync_manager:apply_template_to_table:253 | 成功将 退休人员工资表 模板应用到表 salary_data_2025_01_retired_employees
2025-06-27 11:43:45.478 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 11:43:45.478 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 11:43:45.479 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 11:43:45.479 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 11:43:45.480 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 11:43:45.480 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 11:43:46.253 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 11:44:39.404 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 11:44:39.404 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 11:44:39.405 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 11:44:39.405 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 11:44:39.406 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 11:44:39.406 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 11:44:40.231 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 11:44:40.235 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 11:44:40.235 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 11:44:40.236 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 11:44:40.236 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 11:44:40.237 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 11:44:40.261 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 11:44:40.261 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 11:44:40.262 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 11:44:40.266 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 11:44:40.270 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-27 11:44:40.279 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 11:44:40.282 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-27 11:44:40.285 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 27 个字段
2025-06-27 11:44:40.291 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:311 | 字段映射保存成功: salary_data_2025_01_active_employees_test
2025-06-27 11:44:40.293 | INFO     | src.modules.data_import.config_sync_manager:apply_template_to_table:253 | 成功将 全部在职人员工资表 模板应用到表 salary_data_2025_01_active_employees_test
2025-06-27 11:46:25.989 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 11:46:25.990 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 11:46:25.990 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 11:46:25.990 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 11:46:25.991 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 11:46:25.991 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 11:46:27.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-27 11:46:27.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-27 11:46:27.420 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 11:46:27.421 | INFO     | src.modules.data_import.header_edit_manager:__init__:74 | 表头编辑管理器初始化完成
2025-06-27 11:46:27.425 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1851 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-27 11:46:27.427 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2056 | 表头映射应用完成: 4 个表头
2025-06-27 11:46:27.431 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2056 | 表头映射应用完成: 4 个表头
2025-06-27 11:46:27.432 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2056 | 表头映射应用完成: 7 个表头
2025-06-27 11:46:27.452 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2686 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 11:46:27.453 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2695 | 最大可见行数已更新: 1000 -> 2
2025-06-27 11:46:27.453 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2748 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 11:46:27.454 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2056 | 表头映射应用完成: 4 个表头
2025-06-27 11:46:27.454 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 4 列
2025-06-27 11:46:27.674 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2001 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 221.84ms
2025-06-27 11:48:16.636 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 11:48:16.637 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 11:48:16.637 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 11:48:16.638 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 11:48:16.638 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 11:48:16.639 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 11:48:17.448 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 11:48:17.449 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 11:48:17.451 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 11:48:17.451 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 11:48:17.452 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 11:48:17.472 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 11:48:17.472 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 11:48:17.473 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 11:48:17.473 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 11:48:17.474 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 11:48:17.486 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:311 | 字段映射保存成功: salary_data_2025_01_retired_test
2025-06-27 11:48:17.489 | INFO     | src.modules.data_import.config_sync_manager:apply_template_to_table:253 | 成功将 退休人员工资表 模板应用到表 salary_data_2025_01_retired_test
2025-06-27 11:48:17.489 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:311 | 字段映射保存成功: salary_data_2025_01_active_test
2025-06-27 11:48:17.489 | INFO     | src.modules.data_import.config_sync_manager:apply_template_to_table:253 | 成功将 全部在职人员工资表 模板应用到表 salary_data_2025_01_active_test
2025-06-27 11:48:17.505 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:311 | 字段映射保存成功: salary_data_2025_01_a_grade_test
2025-06-27 11:48:17.506 | INFO     | src.modules.data_import.config_sync_manager:apply_template_to_table:253 | 成功将 A岗职工 模板应用到表 salary_data_2025_01_a_grade_test
2025-06-27 12:58:10.243 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 12:58:10.243 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 12:58:10.244 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 12:58:10.245 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 12:58:10.245 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 12:58:10.246 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 12:58:11.861 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-27 12:58:11.862 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-27 12:58:11.865 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 12:58:11.866 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-06-27 12:58:11.869 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1855 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-27 12:58:11.913 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:311 | 字段映射保存成功: test_refresh_table
2025-06-27 12:58:11.913 | INFO     | src.modules.data_import.config_sync_manager:apply_template_to_table:253 | 成功将 全部在职人员工资表 模板应用到表 test_refresh_table
2025-06-27 12:58:11.915 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 3
2025-06-27 12:58:11.916 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 1000 -> 3
2025-06-27 12:58:11.916 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(3)自动调整最大可见行数为: 3
2025-06-27 12:58:11.917 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 4 个表头
2025-06-27 12:58:11.922 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 3 行, 4 列
2025-06-27 12:58:12.273 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 3 行, 最大可见行数: 3, 耗时: 358.59ms
2025-06-27 12:59:22.109 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 12:59:22.110 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 12:59:22.110 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 12:59:22.111 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 12:59:22.112 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 12:59:22.112 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 12:59:22.997 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 12:59:22.998 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-06-27 12:59:23.004 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:311 | 字段映射保存成功: test_signal_table
2025-06-27 12:59:23.007 | INFO     | src.modules.data_import.config_sync_manager:apply_template_to_table:253 | 成功将 全部在职人员工资表 模板应用到表 test_signal_table
2025-06-27 12:59:23.017 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:425 | 字段映射更新成功: test_signal_table.employee_id -> 新员工编号
2025-06-27 13:00:55.523 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 13:00:55.524 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 13:00:55.524 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 13:00:55.524 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 13:00:55.524 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 13:00:55.524 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 13:00:56.349 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:00:56.357 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:311 | 字段映射保存成功: test_complete_mapping_table
2025-06-27 13:00:56.368 | INFO     | src.modules.data_import.config_sync_manager:apply_template_to_table:253 | 成功将 全部在职人员工资表 模板应用到表 test_complete_mapping_table
2025-06-27 13:00:56.370 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 13:00:56.371 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 13:00:56.372 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 13:00:56.372 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 13:00:56.373 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 13:00:56.386 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 13:00:56.387 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 13:00:56.399 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 13:00:56.400 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 13:00:56.402 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-27 13:00:56.404 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:00:56.404 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-27 13:00:56.404 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 23 个字段
2025-06-27 13:00:56.460 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-06-27 13:00:56.465 | INFO     | src.modules.data_import.config_sync_manager:update_mapping:425 | 字段映射更新成功: test_complete_mapping_table.employee_id -> 员工编号
2025-06-27 13:13:19.583 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 13:13:19.583 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 13:13:19.583 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 13:13:19.583 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 13:13:19.583 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 13:13:19.598 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 13:13:20.840 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-27 13:13:20.840 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-27 13:13:20.840 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 13:13:20.840 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 13:13:20.840 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 13:13:20.840 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 13:13:20.856 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 13:13:20.880 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 13:13:20.881 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 13:13:20.882 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 13:13:20.883 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 13:13:20.883 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-27 13:13:20.884 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-27 13:13:20.885 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-27 13:13:20.886 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-27 13:13:21.218 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1510 | 菜单栏创建完成
2025-06-27 13:13:21.218 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 13:13:21.218 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 13:13:21.218 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-27 13:13:21.234 | INFO     | src.gui.prototype.prototype_main_window:__init__:1486 | 菜单栏管理器初始化完成
2025-06-27 13:13:21.234 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-27 13:13:21.234 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2187 | 管理器设置完成，包含增强版表头管理器
2025-06-27 13:13:21.234 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-27 13:13:21.234 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-27 13:13:21.252 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-06-27 13:13:21.254 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-27 13:13:21.262 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-27 13:13:21.307 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 0个展开项
2025-06-27 13:13:21.308 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-06-27 13:13:21.311 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-06-27 13:13:21.347 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-27 13:13:21.347 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-27 13:13:21.351 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:13:21.351 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-06-27 13:13:21.354 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1855 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-27 13:13:21.354 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 13:13:21.355 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 13:13:21.403 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 48.84ms
2025-06-27 13:13:21.405 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 13:13:21.414 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-27 13:13:21.490 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-27 13:13:21.555 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 13:13:21.556 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 13:13:21.557 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2160 | 快捷键设置完成
2025-06-27 13:13:21.557 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2149 | 主窗口UI设置完成。
2025-06-27 13:13:21.558 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2215 | 信号连接设置完成
2025-06-27 13:13:21.559 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:13:21.563 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2644 | 已加载字段映射信息，共8个表的映射
2025-06-27 13:13:21.563 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 13:13:21.564 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 13:13:21.568 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 13:13:21.569 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 4.61ms
2025-06-27 13:13:21.570 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 13:13:21.611 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 13:13:21.617 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 13:13:21.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 13:13:21.620 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 13:13:21.621 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 3.12ms
2025-06-27 13:13:21.622 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 13:13:21.622 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 13:13:21.623 | INFO     | src.gui.prototype.prototype_main_window:__init__:2101 | 原型主窗口初始化完成
2025-06-27 13:13:21.969 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-27 13:13:21.975 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-27 13:13:21.977 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-27 13:13:22.057 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-06-27 13:13:22.116 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-06-27 13:13:22.149 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-06-27 13:13:22.149 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 13:13:22.149 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 13:13:22.725 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 104 个匹配类型 'salary_data' 的表
2025-06-27 13:13:22.725 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 2 个月份
2025-06-27 13:13:22.725 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 13:13:22.725 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 13:13:22.725 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 13:13:22.725 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 13:13:22.725 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 13:13:22.725 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 13:13:22.741 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 13:13:22.741 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 13:13:22.741 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 13:13:22.741 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 13:13:22.741 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 13:13:22.741 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 26 个月份
2025-06-27 13:13:22.741 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 13:13:22.741 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 13:13:22.942 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-06-27 13:13:22.942 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1277 | 开始获取最新工资数据路径...
2025-06-27 13:13:22.989 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 104 个匹配类型 'salary_data' 的表
2025-06-27 13:13:22.990 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1325 | 找到最新工资数据路径: 工资表 > 2027年 > 02月 > 全部在职人员
2025-06-27 13:13:22.991 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1102 | 重新选择最新数据路径: 工资表 > 2027年 > 02月 > 全部在职人员
2025-06-27 13:13:22.993 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1126 | 重新选择完成: 工资表 > 2027年 > 02月 > 全部在职人员
2025-06-27 13:13:22.996 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 02月 > 全部在职人员
2025-06-27 13:13:22.998 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 13:13:22.998 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 13:13:22.999 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.04ms
2025-06-27 13:13:23.009 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 13:13:23.009 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2735 | 检测到自动选择的最新数据，将显示特殊提示
2025-06-27 13:13:23.018 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 13:13:23.041 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2778 | 数据量大(1396条)，启用分页模式
2025-06-27 13:13:23.045 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2809 | 使用分页模式加载 salary_data_2027_02_active_employees，第1页，每页50条
2025-06-27 13:13:23.048 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2852 | 缓存未命中，从数据库加载: salary_data_2027_02_active_employees 第1页
2025-06-27 13:13:23.062 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_02_active_employees 第1页数据，每页50条
2025-06-27 13:13:23.063 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 13:13:23.074 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 13:13:23.079 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 13:13:23.083 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_02_active_employees 没有字段映射配置
2025-06-27 13:13:23.083 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 13:13:23.084 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2882 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 13:13:23.089 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 13:13:23.090 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:13:23.094 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_02_active_employees 无字段偏好设置，显示所有字段
2025-06-27 13:13:23.095 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 13:13:23.113 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 1000 -> 50
2025-06-27 13:13:23.113 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 13:13:23.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 13:13:23.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 13:13:23.180 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 75.71ms
2025-06-27 13:13:23.191 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 13:13:23.192 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 13:13:40.879 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 2月', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 13:13:40.879 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 2月
2025-06-27 13:13:40.879 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2721321075200 已经注册，将覆盖现有注册
2025-06-27 13:13:40.879 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2721320869984 已经注册，将覆盖现有注册
2025-06-27 13:13:40.879 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 13:13:40.879 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 13:13:40.879 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 13:13:40.879 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 13:13:40.879 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 13:13:40.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 13:13:40.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 13:13:40.911 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 31.37ms
2025-06-27 13:13:40.911 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 13:13:40.911 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 13:13:40.911 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 13:13:40.931 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 13:13:40.936 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 13:13:40.949 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 38.12ms
2025-06-27 13:13:40.950 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 13:13:40.951 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 13:13:40.952 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 2月
2025-06-27 13:13:42.474 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 2月', '工资表 > 2027年 > 2月 > A岗职工', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 13:13:42.474 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 2月 > A岗职工
2025-06-27 13:13:42.474 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2721321075200 已经注册，将覆盖现有注册
2025-06-27 13:13:42.490 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2721320869984 已经注册，将覆盖现有注册
2025-06-27 13:13:42.490 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 13:13:42.490 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 13:13:42.490 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 13:13:42.490 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 13:13:42.490 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 13:13:42.490 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2778 | 数据量大(62条)，启用分页模式
2025-06-27 13:13:42.490 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2809 | 使用分页模式加载 salary_data_2027_02_a_grade_employees，第1页，每页50条
2025-06-27 13:13:42.490 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2852 | 缓存未命中，从数据库加载: salary_data_2027_02_a_grade_employees 第1页
2025-06-27 13:13:42.490 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 2月 > A岗职工
2025-06-27 13:13:42.490 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_02_a_grade_employees 第1页数据，每页50条
2025-06-27 13:13:42.490 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 13:13:42.490 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 13:13:42.490 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 13:13:42.490 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_02_a_grade_employees 没有字段映射配置
2025-06-27 13:13:42.490 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-27 13:13:42.512 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2882 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 13:13:42.536 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 13:13:42.537 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:13:42.558 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 13:13:42.570 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_02_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 13:13:42.575 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 13:13:42.592 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 13:13:42.592 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 13:13:42.592 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 13:13:42.609 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 24.75ms
2025-06-27 13:13:42.609 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 13:13:42.609 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 13:13:45.753 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 2月 > A岗职工', '工资表 > 2027年 > 2月', '工资表 > 2027年 > 2月 > 全部在职人员', '工资表 > 2027年', '工资表']
2025-06-27 13:13:45.753 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 2月 > 全部在职人员
2025-06-27 13:13:45.753 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2721321075200 已经注册，将覆盖现有注册
2025-06-27 13:13:45.753 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2721320869984 已经注册，将覆盖现有注册
2025-06-27 13:13:45.753 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 13:13:45.753 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 13:13:45.753 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 13:13:45.753 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 13:13:45.753 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 13:13:45.753 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2778 | 数据量大(1396条)，启用分页模式
2025-06-27 13:13:45.753 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2809 | 使用分页模式加载 salary_data_2027_02_active_employees，第1页，每页50条
2025-06-27 13:13:45.769 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2818 | 缓存命中: salary_data_2027_02_active_employees 第1页
2025-06-27 13:13:45.769 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_02_active_employees 没有字段映射配置
2025-06-27 13:13:45.769 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2882 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-27 13:13:45.769 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:13:45.769 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_02_active_employees 无字段偏好设置，显示所有字段
2025-06-27 13:13:45.769 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 13:13:45.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 13:13:45.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 13:13:45.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 13:13:45.812 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 42.77ms
2025-06-27 13:13:45.853 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 13:13:45.854 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 13:13:45.856 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 2月 > 全部在职人员
2025-06-27 13:13:49.630 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 13:13:49.630 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:13:49.630 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 13:13:49.630 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 13:13:49.630 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 13:13:49.645 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 13:13:49.645 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 13:13:49.645 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 13:13:49.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 15.66ms
2025-06-27 13:13:49.661 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-27 13:13:49.661 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 13:13:50.808 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 13:13:50.809 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:13:50.810 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 13:13:50.810 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_active_employees 分页获取数据: 第3页, 每页50条
2025-06-27 13:13:50.813 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_active_employees 获取第3页数据: 50 行，总计1396行
2025-06-27 13:13:50.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 13:13:50.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 13:13:50.826 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 13:13:50.849 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 33.48ms
2025-06-27 13:13:50.855 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第3页, 显示50条记录，字段数: 16
2025-06-27 13:13:50.881 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 3
2025-06-27 13:13:51.831 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 13:13:51.832 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:13:51.834 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 13:13:51.834 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_active_employees 分页获取数据: 第4页, 每页50条
2025-06-27 13:13:51.837 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_active_employees 获取第4页数据: 50 行，总计1396行
2025-06-27 13:13:51.847 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 13:13:51.847 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 13:13:51.849 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 13:13:51.874 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 35.22ms
2025-06-27 13:13:51.891 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第4页, 显示50条记录，字段数: 16
2025-06-27 13:13:51.904 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 4
2025-06-27 13:13:52.871 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 13:13:52.873 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:13:52.874 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 13:13:52.874 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_active_employees 分页获取数据: 第5页, 每页50条
2025-06-27 13:13:52.876 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_active_employees 获取第5页数据: 50 行，总计1396行
2025-06-27 13:13:52.887 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 13:13:52.887 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 13:13:52.888 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 13:13:52.908 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 30.11ms
2025-06-27 13:13:52.917 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第5页, 显示50条记录，字段数: 16
2025-06-27 13:13:52.918 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 5
2025-06-27 13:13:53.768 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 13:13:53.770 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:13:53.770 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 13:13:53.770 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_active_employees 分页获取数据: 第6页, 每页50条
2025-06-27 13:13:53.774 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_active_employees 获取第6页数据: 50 行，总计1396行
2025-06-27 13:13:53.785 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 13:13:53.785 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 13:13:53.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 13:13:53.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 94.58ms
2025-06-27 13:13:53.884 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第6页, 显示50条记录，字段数: 16
2025-06-27 13:13:53.885 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 6
2025-06-27 13:13:56.902 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 2月 > 全部在职人员', '工资表 > 2027年 > 2月 > A岗职工', '工资表 > 2027年 > 2月', '工资表 > 2027年 > 2月 > 离休人员', '工资表 > 2027年']
2025-06-27 13:13:56.902 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 2月 > 离休人员
2025-06-27 13:13:56.902 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2721321075200 已经注册，将覆盖现有注册
2025-06-27 13:13:56.902 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2721320869984 已经注册，将覆盖现有注册
2025-06-27 13:13:56.902 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 13:13:56.902 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 13:13:56.902 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 13:13:56.902 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 13:13:56.902 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 13:13:56.902 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2781 | 数据量小(2条)，使用普通模式
2025-06-27 13:13:56.902 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 2月 > 离休人员
2025-06-27 13:13:56.902 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_02_retired_employees 获取数据...
2025-06-27 13:13:56.902 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_02_retired_employees 获取 2 行数据。
2025-06-27 13:13:56.902 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_02_retired_employees 没有字段映射配置
2025-06-27 13:13:56.902 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:13:56.902 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_02_retired_employees 无字段偏好设置，显示所有字段
2025-06-27 13:13:56.918 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-27 13:13:56.938 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 13:13:56.938 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 2
2025-06-27 13:13:56.938 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 13:13:56.938 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-27 13:13:56.964 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 45.25ms
2025-06-27 13:13:56.982 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-27 13:13:58.596 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 2月 > 全部在职人员', '工资表 > 2027年 > 2月 > A岗职工', '工资表 > 2027年 > 2月', '工资表 > 2027年 > 2月 > 退休人员', '工资表 > 2027年 > 2月 > 离休人员']
2025-06-27 13:13:58.596 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 2月 > 退休人员
2025-06-27 13:13:58.596 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2721321075200 已经注册，将覆盖现有注册
2025-06-27 13:13:58.596 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2721320869984 已经注册，将覆盖现有注册
2025-06-27 13:13:58.596 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 13:13:58.596 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 13:13:58.596 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 13:13:58.596 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 13:13:58.596 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 13:13:58.596 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2781 | 数据量小(13条)，使用普通模式
2025-06-27 13:13:58.596 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 2月 > 退休人员
2025-06-27 13:13:58.596 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_02_pension_employees 获取数据...
2025-06-27 13:13:58.596 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_02_pension_employees 获取 13 行数据。
2025-06-27 13:13:58.596 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_02_pension_employees 没有字段映射配置
2025-06-27 13:13:58.612 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:13:58.612 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_02_pension_employees 无字段偏好设置，显示所有字段
2025-06-27 13:13:58.612 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-27 13:13:58.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 2 -> 13
2025-06-27 13:13:58.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-27 13:13:58.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-27 13:13:58.629 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 17.11ms
2025-06-27 13:13:58.629 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-27 13:13:59.830 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 2月 > 全部在职人员', '工资表 > 2027年 > 2月 > A岗职工', '工资表 > 2027年 > 2月', '工资表 > 2027年 > 2月 > 退休人员', '工资表 > 2027年 > 2月 > 离休人员']
2025-06-27 13:13:59.832 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 2月 > 全部在职人员
2025-06-27 13:13:59.834 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2721321075200 已经注册，将覆盖现有注册
2025-06-27 13:13:59.834 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2721320869984 已经注册，将覆盖现有注册
2025-06-27 13:13:59.834 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 13:13:59.835 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 13:13:59.836 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.01ms
2025-06-27 13:13:59.837 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 13:13:59.837 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 13:13:59.838 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2778 | 数据量大(1396条)，启用分页模式
2025-06-27 13:13:59.839 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2809 | 使用分页模式加载 salary_data_2027_02_active_employees，第1页，每页50条
2025-06-27 13:13:59.840 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2818 | 缓存命中: salary_data_2027_02_active_employees 第1页
2025-06-27 13:13:59.843 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_02_active_employees 没有字段映射配置
2025-06-27 13:13:59.843 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2882 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-27 13:13:59.844 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:13:59.845 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_02_active_employees 无字段偏好设置，显示所有字段
2025-06-27 13:13:59.845 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 13:13:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 13 -> 50
2025-06-27 13:13:59.860 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 13:13:59.862 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 13:13:59.886 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 36.52ms
2025-06-27 13:13:59.890 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 13:13:59.924 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 13:13:59.936 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 2月 > 全部在职人员
2025-06-27 13:14:01.379 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 2月 > 全部在职人员', '工资表 > 2027年 > 2月 > A岗职工', '工资表 > 2027年 > 2月', '工资表 > 2027年 > 2月 > 退休人员', '工资表 > 2027年 > 2月 > 离休人员']
2025-06-27 13:14:01.379 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 2月 > A岗职工
2025-06-27 13:14:01.379 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2721321075200 已经注册，将覆盖现有注册
2025-06-27 13:14:01.379 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2721320869984 已经注册，将覆盖现有注册
2025-06-27 13:14:01.379 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 13:14:01.379 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 13:14:01.379 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 13:14:01.379 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 13:14:01.379 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 13:14:01.379 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2778 | 数据量大(62条)，启用分页模式
2025-06-27 13:14:01.379 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2809 | 使用分页模式加载 salary_data_2027_02_a_grade_employees，第1页，每页50条
2025-06-27 13:14:01.379 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2818 | 缓存命中: salary_data_2027_02_a_grade_employees 第1页
2025-06-27 13:14:01.379 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_02_a_grade_employees 没有字段映射配置
2025-06-27 13:14:01.379 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2882 | 分页数据加载成功（缓存）: 50条数据，第1页，总计62条
2025-06-27 13:14:01.379 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 13:14:01.379 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_02_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 13:14:01.379 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 13:14:01.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 13:14:01.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 13:14:01.395 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 13:14:01.426 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 31.39ms
2025-06-27 13:14:01.427 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 13:14:01.429 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 13:14:01.436 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 2月 > A岗职工
2025-06-27 13:15:50.905 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-27 14:08:04.347 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 14:08:04.362 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 14:08:04.362 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 14:08:04.362 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 14:08:04.362 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 14:08:04.362 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 14:08:07.577 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-27 14:08:07.577 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-27 14:08:07.577 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 14:08:07.577 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 14:08:07.577 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 14:08:07.577 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 14:08:07.577 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 14:08:07.639 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 14:08:07.649 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 14:08:07.656 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 14:08:07.663 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 14:08:07.673 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-27 14:08:07.689 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-27 14:08:07.693 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-27 14:08:07.694 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-27 14:08:08.436 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1510 | 菜单栏创建完成
2025-06-27 14:08:08.436 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 14:08:08.436 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 14:08:08.436 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-27 14:08:08.451 | INFO     | src.gui.prototype.prototype_main_window:__init__:1486 | 菜单栏管理器初始化完成
2025-06-27 14:08:08.451 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-27 14:08:08.451 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2187 | 管理器设置完成，包含增强版表头管理器
2025-06-27 14:08:08.509 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:151 | 导航状态加载成功
2025-06-27 14:08:08.536 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-27 14:08:08.624 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-27 14:08:08.685 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-06-27 14:08:08.685 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-27 14:08:08.707 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-27 14:08:08.708 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 4个展开项
2025-06-27 14:08:08.709 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-06-27 14:08:08.709 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-06-27 14:08:08.866 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-27 14:08:08.867 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-27 14:08:08.869 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:08:08.870 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-06-27 14:08:08.875 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1855 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-27 14:08:08.877 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 14:08:08.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 14:08:08.880 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 4.64ms
2025-06-27 14:08:08.881 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 14:08:08.912 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-27 14:08:09.050 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-27 14:08:09.062 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 14:08:09.063 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 14:08:09.064 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2160 | 快捷键设置完成
2025-06-27 14:08:09.064 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2149 | 主窗口UI设置完成。
2025-06-27 14:08:09.065 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2215 | 信号连接设置完成
2025-06-27 14:08:09.066 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:08:09.070 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2644 | 已加载字段映射信息，共8个表的映射
2025-06-27 14:08:09.070 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 14:08:09.072 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 14:08:09.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 14:08:09.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.59ms
2025-06-27 14:08:09.074 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 14:08:09.075 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:08:09.076 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 14:08:09.076 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 14:08:09.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 14:08:09.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.07ms
2025-06-27 14:08:09.079 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 14:08:09.083 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:08:09.084 | INFO     | src.gui.prototype.prototype_main_window:__init__:2101 | 原型主窗口初始化完成
2025-06-27 14:08:09.616 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-27 14:08:09.621 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-06-27 14:08:09.622 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-06-27 14:08:09.623 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-27 14:08:09.623 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-27 14:08:09.631 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-06-27 14:08:09.640 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 14:08:09.640 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 14:08:10.190 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 104 个匹配类型 'salary_data' 的表
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 2 个月份
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 26 个月份
2025-06-27 14:08:10.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 14:08:10.206 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 14:08:10.406 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-06-27 14:08:10.406 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1277 | 开始获取最新工资数据路径...
2025-06-27 14:08:10.441 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 104 个匹配类型 'salary_data' 的表
2025-06-27 14:08:10.443 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1325 | 找到最新工资数据路径: 工资表 > 2027年 > 02月 > 全部在职人员
2025-06-27 14:08:10.452 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1102 | 重新选择最新数据路径: 工资表 > 2027年 > 02月 > 全部在职人员
2025-06-27 14:08:10.455 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1126 | 重新选择完成: 工资表 > 2027年 > 02月 > 全部在职人员
2025-06-27 14:08:10.462 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 02月 > 全部在职人员
2025-06-27 14:08:10.471 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 14:08:10.473 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 14:08:10.484 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 11.47ms
2025-06-27 14:08:10.489 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 14:08:10.496 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2735 | 检测到自动选择的最新数据，将显示特殊提示
2025-06-27 14:08:10.500 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:08:10.503 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2778 | 数据量大(1396条)，启用分页模式
2025-06-27 14:08:10.507 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2809 | 使用分页模式加载 salary_data_2027_02_active_employees，第1页，每页50条
2025-06-27 14:08:10.508 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2852 | 缓存未命中，从数据库加载: salary_data_2027_02_active_employees 第1页
2025-06-27 14:08:10.508 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_02_active_employees 第1页数据，每页50条
2025-06-27 14:08:10.508 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 14:08:10.531 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 14:08:10.532 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 14:08:10.536 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_02_active_employees 没有字段映射配置
2025-06-27 14:08:10.537 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 14:08:10.538 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2882 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 14:08:10.581 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_02_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 14:08:10.582 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:08:10.602 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_02_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 14:08:10.614 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_02_active_employees 无字段偏好设置，显示所有字段
2025-06-27 14:08:10.618 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 14:08:10.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 1000 -> 50
2025-06-27 14:08:10.655 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 14:08:10.685 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 14:08:10.718 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 98.80ms
2025-06-27 14:08:10.726 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 14:08:10.728 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 14:08:38.050 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:396 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-27 14:08:38.050 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:2447 | 接收到数据导入请求，推断的目标路径: 工资表 > 2027年 > 02月 > 全部在职人员。打开导入对话框。
2025-06-27 14:08:38.066 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-27 14:08:38.066 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:08:38.066 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-27 14:08:38.066 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-06-27 14:08:38.085 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2027年 > 02月 > 全部在职人员
2025-06-27 14:08:38.133 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 104 个匹配类型 'salary_data' 的表
2025-06-27 14:08:38.278 | INFO     | src.gui.dialogs:_init_field_mapping:1926 | 初始化字段映射表格: 17 个默认字段
2025-06-27 14:08:38.405 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-27 14:08:38.406 | INFO     | src.gui.dialogs:_apply_default_settings:2142 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-06-27 14:08:38.407 | INFO     | src.gui.dialogs:_setup_tooltips:2397 | 工具提示设置完成
2025-06-27 14:08:38.407 | INFO     | src.gui.dialogs:_setup_shortcuts:2436 | 快捷键设置完成
2025-06-27 14:08:38.408 | INFO     | src.gui.dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-06-27 14:08:47.456 | INFO     | src.gui.dialogs:_on_target_changed:2081 | 目标位置已更新: 工资表 > 2027年 > 3月 > 全部在职人员
2025-06-27 14:09:05.444 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 14:09:07.653 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 14:09:07.656 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-06-27 14:09:07.656 | INFO     | src.gui.dialogs:_auto_select_sheet_by_category:2177 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-06-27 14:09:34.055 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 14:09:34.318 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 14:09:34.332 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 14:09:34.333 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:194 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 14:09:34.334 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 14:09:34.607 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 14:09:34.607 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:205 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 14:09:34.607 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 14:09:34.607 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 14:09:34.607 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 14:09:34.771 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-27 14:09:34.771 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 14:09:34.771 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-06-27 14:09:34.771 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-27 14:09:34.771 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-06-27 14:09:34.771 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-27 14:09:34.771 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 16 个字段
2025-06-27 14:09:34.771 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 离休人员工资表 模板生成字段映射
2025-06-27 14:09:34.787 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_03_retired_employees
2025-06-27 14:09:34.787 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_03_retired_employees 生成标准化字段映射: 16 个字段
2025-06-27 14:09:34.787 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet 离休人员工资表 存在 1 个验证错误
2025-06-27 14:09:34.787 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-06-27 14:09:34.787 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_name']
2025-06-27 14:09:34.787 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_03_retired_employees 不存在，将根据模板创建...
2025-06-27 14:09:34.817 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_03_retired_employees 保存 2 条数据。
2025-06-27 14:09:34.818 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 14:09:34.818 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 14:09:34.820 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 14:09:34.982 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-27 14:09:35.016 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 14:09:35.017 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-06-27 14:09:35.017 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-27 14:09:35.017 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-06-27 14:09:35.017 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-27 14:09:35.017 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 27 个字段
2025-06-27 14:09:35.017 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 退休人员工资表 模板生成字段映射
2025-06-27 14:09:35.029 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_03_pension_employees
2025-06-27 14:09:35.030 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_03_pension_employees 生成标准化字段映射: 27 个字段
2025-06-27 14:09:35.030 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet 退休人员工资表 存在 1 个验证错误
2025-06-27 14:09:35.034 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-06-27 14:09:35.043 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 14:09:35.064 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_03_pension_employees 不存在，将根据模板创建...
2025-06-27 14:09:35.086 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_03_pension_employees 保存 13 条数据。
2025-06-27 14:09:35.104 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 14:09:35.105 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 14:09:35.107 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 14:09:35.304 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-27 14:09:35.318 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 14:09:35.318 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-27 14:09:35.328 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-27 14:09:35.338 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-06-27 14:09:35.339 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-06-27 14:09:35.341 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 23 个字段
2025-06-27 14:09:35.341 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 全部在职人员工资表 模板生成字段映射
2025-06-27 14:09:35.347 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_03_active_employees
2025-06-27 14:09:35.347 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_03_active_employees 生成标准化字段映射: 23 个字段
2025-06-27 14:09:35.349 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet 全部在职人员工资表 存在 2 个验证错误
2025-06-27 14:09:35.354 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-06-27 14:09:35.366 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 14:09:35.379 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_03_active_employees 不存在，将根据模板创建...
2025-06-27 14:09:35.497 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_03_active_employees 保存 1396 条数据。
2025-06-27 14:09:35.512 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 14:09:35.516 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 14:09:35.517 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 14:09:35.689 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-27 14:09:35.763 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 14:09:35.763 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-06-27 14:09:35.763 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-27 14:09:35.763 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-06-27 14:09:35.763 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-06-27 14:09:35.775 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 21 个字段
2025-06-27 14:09:35.776 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 A岗职工 模板生成字段映射
2025-06-27 14:09:35.784 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_03_a_grade_employees
2025-06-27 14:09:35.785 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_03_a_grade_employees 生成标准化字段映射: 21 个字段
2025-06-27 14:09:35.785 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet A岗职工 存在 2 个验证错误
2025-06-27 14:09:35.793 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet A岗职工 数据处理完成: 62 行
2025-06-27 14:09:35.799 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 14:09:35.801 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_03_a_grade_employees 不存在，将根据模板创建...
2025-06-27 14:09:35.828 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_03_a_grade_employees 保存 62 条数据。
2025-06-27 14:09:35.873 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:224 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_03_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2027_03_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_03_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_03_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_03_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_03_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_03_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_03_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-27 14:09:35.876 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_03_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2027_03_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_03_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_03_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_03_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_03_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_03_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_03_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2027-03', 'data_description': '2027年3月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2027年 > 3月 > 全部在职人员'}
2025-06-27 14:09:35.877 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2460 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_03_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2027_03_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_03_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_03_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_03_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_03_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_03_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_03_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2027-03', 'data_description': '2027年3月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2027年 > 3月 > 全部在职人员'}
2025-06-27 14:09:35.877 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2471 | 导入模式: multi_sheet, 目标路径: '工资表 > 2027年 > 3月 > 全部在职人员'
2025-06-27 14:09:35.877 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2479 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2027年 > 3月 > 全部在职人员
2025-06-27 14:09:35.877 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2537 | 检查是否需要更新导航面板: ['工资表', '2027年', '3月', '全部在职人员']
2025-06-27 14:09:35.877 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2541 | 检测到工资数据导入，开始刷新导航面板
2025-06-27 14:09:35.877 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2545 | 使用强制刷新方法
2025-06-27 14:09:35.886 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 14:09:35.968 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 14:09:36.030 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 108 个匹配类型 'salary_data' 的表
2025-06-27 14:09:36.068 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 3 个月份
2025-06-27 14:09:36.071 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 14:09:36.071 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 14:09:36.071 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 14:09:36.071 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 14:09:36.080 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 14:09:36.083 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 14:09:36.083 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 14:09:36.084 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 14:09:36.085 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 14:09:36.086 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 14:09:36.086 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 14:09:36.087 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 27 个月份
2025-06-27 14:09:36.088 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 14:09:36.088 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 14:09:36.089 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2550 | 将在800ms后导航到: 工资表 > 2027年 > 3月 > 全部在职人员
2025-06-27 14:09:36.923 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2583 | 尝试导航到新导入的路径: 工资表 > 2027年 > 3月 > 全部在职人员
2025-06-27 14:09:36.928 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 3月 > 全部在职人员', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 14:09:36.928 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 3月 > 全部在职人员
2025-06-27 14:09:36.934 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1569812070912 已经注册，将覆盖现有注册
2025-06-27 14:09:36.935 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1569811865696 已经注册，将覆盖现有注册
2025-06-27 14:09:36.935 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 4 个表格到表头管理器
2025-06-27 14:09:36.935 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 14:09:36.936 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.01ms
2025-06-27 14:09:36.936 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 14:09:36.936 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:09:36.939 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2778 | 数据量大(1396条)，启用分页模式
2025-06-27 14:09:36.939 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2809 | 使用分页模式加载 salary_data_2027_03_active_employees，第1页，每页50条
2025-06-27 14:09:36.940 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2852 | 缓存未命中，从数据库加载: salary_data_2027_03_active_employees 第1页
2025-06-27 14:09:36.940 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 3月 > 全部在职人员
2025-06-27 14:09:36.941 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_03_active_employees 第1页数据，每页50条
2025-06-27 14:09:36.941 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2588 | 已成功导航到新导入的路径: 工资表 > 2027年 > 3月 > 全部在职人员
2025-06-27 14:09:36.942 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_03_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 14:09:36.952 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_03_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 14:09:36.952 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 14:09:36.954 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2670 | 字段映射应用成功: 5 个字段重命名
2025-06-27 14:09:36.956 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 14:09:36.972 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2882 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 14:09:37.006 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_03_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 14:09:37.008 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:09:37.030 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_03_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 14:09:37.042 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_03_active_employees 无字段偏好设置，显示所有字段
2025-06-27 14:09:37.059 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 14:09:37.075 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 14:09:37.130 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 14:09:37.134 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 14:09:37.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 14:09:37.179 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 117.31ms
2025-06-27 14:09:37.239 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 14:09:37.241 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 14:09:51.618 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 14:09:51.618 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:09:51.618 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 14:09:51.618 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_03_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 14:09:51.618 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_03_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 14:09:51.637 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 14:09:51.637 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 14:09:51.650 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 14:09:51.650 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 14:09:51.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 27.94ms
2025-06-27 14:09:51.665 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-27 14:09:51.665 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 14:09:59.828 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 3月 > 全部在职人员', '工资表 > 2027年', '工资表 > 2027年 > 3月 > 退休人员', '工资表 > 2027年 > 3月', '工资表']
2025-06-27 14:09:59.829 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 3月 > 退休人员
2025-06-27 14:09:59.835 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1569812070912 已经注册，将覆盖现有注册
2025-06-27 14:09:59.836 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1569811865696 已经注册，将覆盖现有注册
2025-06-27 14:09:59.836 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_1570045151856 已经注册，将覆盖现有注册
2025-06-27 14:09:59.836 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_1570045120096 已经注册，将覆盖现有注册
2025-06-27 14:09:59.836 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 4 个表格到表头管理器
2025-06-27 14:09:59.837 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 14:09:59.837 | WARNING  | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:507 | 检测到表格 table_1_1569811865696 的表头重影: ['工号', 'id']
2025-06-27 14:09:59.838 | WARNING  | src.gui.table_header_manager:_clear_single_table_header:196 | 检测到表格 table_1_1569811865696 存在表头重影: ['工号', 'id']
2025-06-27 14:09:59.839 | WARNING  | src.gui.prototype.prototype_main_window:_on_header_shadow_detected:3461 | 检测到表格 table_1_1569811865696 表头重影: ['工号', 'id']
2025-06-27 14:10:00.041 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:519 | 表格 table_1_1569811865696 表头重影修复成功
2025-06-27 14:10:00.043 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 205.35ms
2025-06-27 14:10:00.052 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：1 个重影表格，1 个修复成功，0 个修复失败
2025-06-27 14:10:00.053 | INFO     | src.gui.prototype.prototype_main_window:_pre_clear_headers_on_navigation_change:3385 | 导航切换预清理：修复了 1 个表格的表头重影
2025-06-27 14:10:00.054 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:10:00.055 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2781 | 数据量小(13条)，使用普通模式
2025-06-27 14:10:00.056 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 3月 > 退休人员
2025-06-27 14:10:00.056 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_03_pension_employees 获取数据...
2025-06-27 14:10:00.059 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_03_pension_employees 获取 13 行数据。
2025-06-27 14:10:00.061 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2670 | 字段映射应用成功: 5 个字段重命名
2025-06-27 14:10:00.065 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:10:00.066 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_03_pension_employees 无字段偏好设置，显示所有字段
2025-06-27 14:10:00.071 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-27 14:10:00.094 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 13
2025-06-27 14:10:00.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-27 14:10:00.161 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 14:10:00.161 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-27 14:10:00.180 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 106.64ms
2025-06-27 14:10:00.187 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-27 14:10:01.371 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 3月 > 全部在职人员', '工资表 > 2027年 > 3月 > 退休人员', '工资表 > 2027年', '工资表 > 2027年 > 3月 > A岗职工', '工资表 > 2027年 > 3月']
2025-06-27 14:10:01.372 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 3月 > A岗职工
2025-06-27 14:10:01.380 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_1569812070912 已经注册，将覆盖现有注册
2025-06-27 14:10:01.381 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_1569811865696 已经注册，将覆盖现有注册
2025-06-27 14:10:01.381 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_1570045151856 已经注册，将覆盖现有注册
2025-06-27 14:10:01.383 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_1570045120096 已经注册，将覆盖现有注册
2025-06-27 14:10:01.383 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 4 个表格到表头管理器
2025-06-27 14:10:01.384 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 14:10:01.385 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.60ms
2025-06-27 14:10:01.386 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 14:10:01.387 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:10:01.388 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2778 | 数据量大(62条)，启用分页模式
2025-06-27 14:10:01.388 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2809 | 使用分页模式加载 salary_data_2027_03_a_grade_employees，第1页，每页50条
2025-06-27 14:10:01.389 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2852 | 缓存未命中，从数据库加载: salary_data_2027_03_a_grade_employees 第1页
2025-06-27 14:10:01.391 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 3月 > A岗职工
2025-06-27 14:10:01.391 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_03_a_grade_employees 第1页数据，每页50条
2025-06-27 14:10:01.393 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_03_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 14:10:01.398 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_03_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 14:10:01.399 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 14:10:01.401 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2670 | 字段映射应用成功: 5 个字段重命名
2025-06-27 14:10:01.402 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-27 14:10:01.481 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2882 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 14:10:01.496 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_03_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 14:10:01.497 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:10:01.499 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_03_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 14:10:01.502 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_03_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 14:10:01.565 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 14:10:01.607 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 13 -> 50
2025-06-27 14:10:01.608 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 14:10:01.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 14:10:01.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 14:10:01.654 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 56.09ms
2025-06-27 14:10:01.698 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 14:10:01.698 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 14:10:16.982 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:420 | 刷新数据功能被触发，将强制刷新导航面板。
2025-06-27 14:10:16.982 | WARNING  | src.gui.prototype.prototype_main_window:_on_refresh_data:435 | 导航面板不支持强制刷新。
2025-06-27 14:10:21.199 | INFO     | __main__:main:302 | 应用程序正常退出
2025-06-27 14:19:21.362 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-06-27 14:19:21.362 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-06-27 14:19:21.362 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-06-27 14:19:21.362 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-06-27 14:19:21.363 | INFO     | src.utils.log_config:_log_initialization_info:212 | 日志文件路径: C:\test\salary_changes\salary_changes\logs\salary_system.log
2025-06-27 14:19:21.364 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-06-27 14:19:22.606 | INFO     | __main__:setup_app_logging:206 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-06-27 14:19:22.621 | INFO     | __main__:main:270 | 初始化核心管理器...
2025-06-27 14:19:22.621 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 14:19:22.621 | INFO     | src.modules.system_config.config_manager:load_config:338 | 正在加载配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 14:19:22.621 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-06-27 14:19:22.621 | INFO     | src.modules.system_config.config_manager:load_config:350 | 配置文件加载成功
2025-06-27 14:19:22.621 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-06-27 14:19:22.621 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-06-27 14:19:22.621 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-06-27 14:19:22.640 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 14:19:22.640 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:102 | 动态表管理器初始化完成
2025-06-27 14:19:22.640 | INFO     | __main__:main:275 | 核心管理器初始化完成。
2025-06-27 14:19:22.641 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-06-27 14:19:22.641 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-06-27 14:19:22.642 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-06-27 14:19:22.951 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:1510 | 菜单栏创建完成
2025-06-27 14:19:22.951 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 14:19:22.951 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 14:19:22.951 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-06-27 14:19:22.951 | INFO     | src.gui.prototype.prototype_main_window:__init__:1486 | 菜单栏管理器初始化完成
2025-06-27 14:19:22.951 | INFO     | src.gui.table_header_manager:__init__:68 | 表头管理器初始化完成
2025-06-27 14:19:22.951 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:2187 | 管理器设置完成，包含增强版表头管理器
2025-06-27 14:19:22.951 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-06-27 14:19:22.951 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-06-27 14:19:22.967 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1042 | 开始从元数据动态加载工资数据...
2025-06-27 14:19:22.967 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1049 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-06-27 14:19:22.971 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:680 | 导航面板已重构：移除功能性导航，专注数据导航
2025-06-27 14:19:22.973 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:715 | 恢复导航状态: 0个展开项
2025-06-27 14:19:22.973 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-06-27 14:19:22.974 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:521 | 增强导航面板初始化完成
2025-06-27 14:19:23.041 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1264 | 快捷键注册完成: 18/18 个
2025-06-27 14:19:23.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1475 | 拖拽排序管理器初始化完成
2025-06-27 14:19:23.047 | INFO     | src.modules.data_import.config_sync_manager:_initialize_config:94 | 默认配置文件创建成功，包含4类工资表模板
2025-06-27 14:19:23.048 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:19:23.048 | INFO     | src.modules.data_import.header_edit_manager:__init__:81 | 表头编辑管理器初始化完成
2025-06-27 14:19:23.078 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1855 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-06-27 14:19:23.095 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 14:19:23.100 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 14:19:23.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 7.84ms
2025-06-27 14:19:23.104 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 14:19:23.113 | INFO     | src.gui.widgets.pagination_widget:__init__:88 | 分页组件初始化完成
2025-06-27 14:19:23.186 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:379 | 控制面板按钮信号连接完成
2025-06-27 14:19:23.249 | INFO     | src.modules.system_config.config_manager:__init__:311 | 配置管理器初始化完成，配置文件: C:\test\salary_changes\salary_changes\config.json
2025-06-27 14:19:23.254 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-06-27 14:19:23.255 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:2160 | 快捷键设置完成
2025-06-27 14:19:23.258 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:2149 | 主窗口UI设置完成。
2025-06-27 14:19:23.258 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:2215 | 信号连接设置完成
2025-06-27 14:19:23.260 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:19:23.261 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:2644 | 已加载字段映射信息，共0个表的映射
2025-06-27 14:19:23.261 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 14:19:23.263 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 14:19:23.268 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 14:19:23.269 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 6.78ms
2025-06-27 14:19:23.270 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 14:19:23.271 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:19:23.271 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 14:19:23.272 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-06-27 14:19:23.273 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 14:19:23.274 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 1000, 耗时: 2.64ms
2025-06-27 14:19:23.285 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 14:19:23.349 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:19:23.350 | INFO     | src.gui.prototype.prototype_main_window:__init__:2101 | 原型主窗口初始化完成
2025-06-27 14:19:23.778 | INFO     | __main__:main:297 | 应用程序启动成功
2025-06-27 14:19:23.782 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1003 | 执行延迟的自动选择最新数据...
2025-06-27 14:19:23.782 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1014 | 导航树即将刷新，跳过当前自动选择，将在刷新后执行
2025-06-27 14:19:23.783 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1266px)
2025-06-27 14:19:23.784 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1241 | MainWorkspaceArea 响应式适配: sm
2025-06-27 14:19:23.788 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1064 | 执行延迟的工资数据加载...
2025-06-27 14:19:23.788 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 14:19:23.788 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 14:19:23.853 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 108 个匹配类型 'salary_data' 的表
2025-06-27 14:19:23.937 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 3 个月份
2025-06-27 14:19:23.960 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 14:19:23.960 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 14:19:23.960 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 14:19:23.960 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 14:19:23.960 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 14:19:23.960 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 14:19:23.960 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 14:19:23.960 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 14:19:23.960 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 14:19:23.960 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 14:19:23.976 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 14:19:23.979 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 27 个月份
2025-06-27 14:19:23.980 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 14:19:23.981 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 14:19:24.181 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1093 | 导航树刷新完成，重新执行自动选择...
2025-06-27 14:19:24.181 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1277 | 开始获取最新工资数据路径...
2025-06-27 14:19:24.214 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 108 个匹配类型 'salary_data' 的表
2025-06-27 14:19:24.214 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1325 | 找到最新工资数据路径: 工资表 > 2027年 > 03月 > 全部在职人员
2025-06-27 14:19:24.214 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1102 | 重新选择最新数据路径: 工资表 > 2027年 > 03月 > 全部在职人员
2025-06-27 14:19:24.214 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1126 | 重新选择完成: 工资表 > 2027年 > 03月 > 全部在职人员
2025-06-27 14:19:24.214 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 03月 > 全部在职人员
2025-06-27 14:19:24.214 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 14:19:24.214 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 14:19:24.214 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 14:19:24.214 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 14:19:24.214 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2735 | 检测到自动选择的最新数据，将显示特殊提示
2025-06-27 14:19:24.214 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:19:24.230 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2778 | 数据量大(1396条)，启用分页模式
2025-06-27 14:19:24.244 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2809 | 使用分页模式加载 salary_data_2027_03_active_employees，第1页，每页50条
2025-06-27 14:19:24.245 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2852 | 缓存未命中，从数据库加载: salary_data_2027_03_active_employees 第1页
2025-06-27 14:19:24.287 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_03_active_employees 第1页数据，每页50条
2025-06-27 14:19:24.289 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_03_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 14:19:24.300 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_03_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 14:19:24.302 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 14:19:24.303 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_03_active_employees 没有字段映射配置
2025-06-27 14:19:24.304 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 14:19:24.308 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2882 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 14:19:24.315 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_03_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 14:19:24.316 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:19:24.317 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_03_active_employees 无字段偏好设置，显示所有字段
2025-06-27 14:19:24.317 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 14:19:24.339 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 1000 -> 50
2025-06-27 14:19:24.342 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_03_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 14:19:24.367 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 14:19:24.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 14:19:24.431 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 109.86ms
2025-06-27 14:19:24.452 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 14:19:24.454 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 14:19:37.588 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 3月', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 14:19:37.588 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 3月
2025-06-27 14:19:37.588 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2128019538432 已经注册，将覆盖现有注册
2025-06-27 14:19:37.588 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2128019333216 已经注册，将覆盖现有注册
2025-06-27 14:19:37.588 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 14:19:37.588 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 14:19:37.588 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 14:19:37.588 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 14:19:37.588 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 14:19:37.604 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 14:19:37.604 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 14:19:37.619 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 31.33ms
2025-06-27 14:19:37.619 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 14:19:37.619 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:19:37.619 | WARNING  | src.gui.prototype.prototype_main_window:set_data:460 | 尝试设置空数据，将显示提示信息。
2025-06-27 14:19:37.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2728 | 数据量为0，不调整最大可见行数，保持当前值: 50
2025-06-27 14:19:37.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 0 行, 7 列
2025-06-27 14:19:37.675 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 0 行, 最大可见行数: 50, 耗时: 55.34ms
2025-06-27 14:19:37.701 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1093 | 表格已初始化为空白状态，等待用户导入数据
2025-06-27 14:19:37.702 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:19:37.703 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 3月
2025-06-27 14:19:40.706 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 3月', '工资表 > 2027年 > 3月 > A岗职工', '工资表 > 2027年', '工资表', '工资表 > 2025年']
2025-06-27 14:19:40.706 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 3月 > A岗职工
2025-06-27 14:19:40.706 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2128019538432 已经注册，将覆盖现有注册
2025-06-27 14:19:40.706 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2128019333216 已经注册，将覆盖现有注册
2025-06-27 14:19:40.706 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 14:19:40.722 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 14:19:40.722 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 14:19:40.722 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 14:19:40.722 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:19:40.722 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2778 | 数据量大(62条)，启用分页模式
2025-06-27 14:19:40.722 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2809 | 使用分页模式加载 salary_data_2027_03_a_grade_employees，第1页，每页50条
2025-06-27 14:19:40.722 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2852 | 缓存未命中，从数据库加载: salary_data_2027_03_a_grade_employees 第1页
2025-06-27 14:19:40.722 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 3月 > A岗职工
2025-06-27 14:19:40.722 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_03_a_grade_employees 第1页数据，每页50条
2025-06-27 14:19:40.722 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_03_a_grade_employees 分页获取数据: 第1页, 每页50条
2025-06-27 14:19:40.722 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_03_a_grade_employees 获取第1页数据: 50 行，总计62行
2025-06-27 14:19:40.722 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 14:19:40.722 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_03_a_grade_employees 没有字段映射配置
2025-06-27 14:19:40.722 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 62
2025-06-27 14:19:40.738 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2882 | 分页数据加载成功（数据库）: 50条数据，第1页，总计62条
2025-06-27 14:19:40.738 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_03_a_grade_employees 分页获取数据: 第2页, 每页50条
2025-06-27 14:19:40.738 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:19:40.738 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_03_a_grade_employees 无字段偏好设置，显示所有字段
2025-06-27 14:19:40.738 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 14:19:40.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 14:19:40.773 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_03_a_grade_employees 获取第2页数据: 12 行，总计62行
2025-06-27 14:19:40.774 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 14:19:40.811 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 14:19:40.834 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 81.15ms
2025-06-27 14:19:40.834 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 14:19:40.834 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 62
2025-06-27 14:19:42.457 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 3月 > A岗职工', '工资表 > 2027年 > 3月', '工资表 > 2027年 > 3月 > 全部在职人员', '工资表 > 2027年', '工资表']
2025-06-27 14:19:42.457 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 3月 > 全部在职人员
2025-06-27 14:19:42.473 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2128019538432 已经注册，将覆盖现有注册
2025-06-27 14:19:42.473 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2128019333216 已经注册，将覆盖现有注册
2025-06-27 14:19:42.473 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 14:19:42.473 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 14:19:42.473 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 14:19:42.473 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 14:19:42.473 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:19:42.473 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2778 | 数据量大(1396条)，启用分页模式
2025-06-27 14:19:42.473 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2809 | 使用分页模式加载 salary_data_2027_03_active_employees，第1页，每页50条
2025-06-27 14:19:42.473 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2818 | 缓存命中: salary_data_2027_03_active_employees 第1页
2025-06-27 14:19:42.473 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_03_active_employees 没有字段映射配置
2025-06-27 14:19:42.473 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2882 | 分页数据加载成功（缓存）: 50条数据，第1页，总计1396条
2025-06-27 14:19:42.473 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:19:42.473 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_03_active_employees 无字段偏好设置，显示所有字段
2025-06-27 14:19:42.473 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 14:19:42.488 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 14:19:42.488 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 14:19:42.488 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 14:19:42.547 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 73.99ms
2025-06-27 14:19:42.559 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 14:19:42.559 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 14:19:42.564 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 3月 > 全部在职人员
2025-06-27 14:19:44.174 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 3月 > 全部在职人员', '工资表 > 2027年 > 3月 > A岗职工', '工资表 > 2027年 > 3月', '工资表 > 2027年 > 3月 > 退休人员', '工资表 > 2027年']
2025-06-27 14:19:44.190 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 3月 > 退休人员
2025-06-27 14:19:44.190 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2128019538432 已经注册，将覆盖现有注册
2025-06-27 14:19:44.190 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2128019333216 已经注册，将覆盖现有注册
2025-06-27 14:19:44.190 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 14:19:44.190 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 14:19:44.190 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 14:19:44.190 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 14:19:44.190 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:19:44.190 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2781 | 数据量小(13条)，使用普通模式
2025-06-27 14:19:44.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 3月 > 退休人员
2025-06-27 14:19:44.190 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_03_pension_employees 获取数据...
2025-06-27 14:19:44.190 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_03_pension_employees 获取 13 行数据。
2025-06-27 14:19:44.190 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_03_pension_employees 没有字段映射配置
2025-06-27 14:19:44.190 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:19:44.190 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_03_pension_employees 无字段偏好设置，显示所有字段
2025-06-27 14:19:44.205 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-27 14:19:44.239 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 13
2025-06-27 14:19:44.272 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-27 14:19:44.274 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-27 14:19:44.290 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 84.23ms
2025-06-27 14:19:44.291 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-27 14:19:45.589 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 3月 > 全部在职人员', '工资表 > 2027年 > 3月 > A岗职工', '工资表 > 2027年 > 3月', '工资表 > 2027年 > 3月 > 离休人员', '工资表 > 2027年 > 3月 > 退休人员']
2025-06-27 14:19:45.590 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 3月 > 离休人员
2025-06-27 14:19:45.591 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2128019538432 已经注册，将覆盖现有注册
2025-06-27 14:19:45.592 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2128019333216 已经注册，将覆盖现有注册
2025-06-27 14:19:45.592 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 2 个表格到表头管理器
2025-06-27 14:19:45.593 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 2 个表格
2025-06-27 14:19:45.594 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 1.02ms
2025-06-27 14:19:45.594 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 14:19:45.595 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:19:45.596 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2781 | 数据量小(2条)，使用普通模式
2025-06-27 14:19:45.596 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 3月 > 离休人员
2025-06-27 14:19:45.597 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_03_retired_employees 获取数据...
2025-06-27 14:19:45.599 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_03_retired_employees 获取 2 行数据。
2025-06-27 14:19:45.600 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_03_retired_employees 没有字段映射配置
2025-06-27 14:19:45.602 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:19:45.602 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_03_retired_employees 无字段偏好设置，显示所有字段
2025-06-27 14:19:45.614 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-27 14:19:45.624 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 14:19:45.625 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 13 -> 2
2025-06-27 14:19:45.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 14:19:45.627 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-27 14:19:45.641 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 23.97ms
2025-06-27 14:19:45.682 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-27 14:19:49.334 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:396 | 数据导入功能被触发，发出 import_requested 信号。
2025-06-27 14:19:49.334 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:2447 | 接收到数据导入请求，推断的目标路径: 工资表 > 2027年 > 3月 > 离休人员。打开导入对话框。
2025-06-27 14:19:49.334 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-06-27 14:19:49.334 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:19:49.334 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:64 | 多Sheet导入器初始化完成
2025-06-27 14:19:49.334 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-06-27 14:19:49.352 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2027年 > 3月 > 离休人员
2025-06-27 14:19:49.400 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 108 个匹配类型 'salary_data' 的表
2025-06-27 14:19:49.482 | INFO     | src.gui.dialogs:_init_field_mapping:1926 | 初始化字段映射表格: 17 个默认字段
2025-06-27 14:19:49.681 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-06-27 14:19:49.683 | INFO     | src.gui.dialogs:_on_import_mode_changed:2599 | 切换到单Sheet导入模式
2025-06-27 14:19:49.683 | INFO     | src.gui.dialogs:_apply_default_settings:2142 | 已应用默认设置: {'start_row': 1, 'import_mode': 'single_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'merge_to_single_table', 'table_template': 'salary_data'}
2025-06-27 14:19:49.685 | INFO     | src.gui.dialogs:_setup_tooltips:2397 | 工具提示设置完成
2025-06-27 14:19:49.685 | INFO     | src.gui.dialogs:_setup_shortcuts:2436 | 快捷键设置完成
2025-06-27 14:19:49.686 | INFO     | src.gui.dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-06-27 14:19:52.467 | INFO     | src.gui.dialogs:_on_target_changed:2081 | 目标位置已更新: 工资表 > 2027年 > 4月 > 离休人员
2025-06-27 14:19:55.723 | INFO     | src.gui.dialogs:_on_target_changed:2081 | 目标位置已更新: 工资表 > 2027年 > 4月 > 全部在职人员
2025-06-27 14:20:02.345 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 14:20:02.573 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 14:20:02.576 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-06-27 14:20:02.578 | INFO     | src.gui.dialogs:_auto_select_sheet_by_category:2177 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-06-27 14:20:15.855 | INFO     | src.gui.dialogs:_on_import_mode_changed:2593 | 切换到多Sheet导入模式
2025-06-27 14:20:26.314 | INFO     | src.modules.data_import.excel_importer:preview_data:221 | 数据预览完成: 23列, 20行
2025-06-27 14:20:45.338 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 14:20:45.592 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 14:20:45.605 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 14:20:45.606 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:194 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 14:20:45.610 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 14:20:45.833 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 14:20:45.835 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:205 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-06-27 14:20:45.836 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 14:20:45.837 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 14:20:45.838 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 14:20:45.994 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 3行 x 12列
2025-06-27 14:20:45.997 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 14:20:45.997 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行3]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '合计': np.float64(34405.100000000006)}
2025-06-27 14:20:45.998 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-06-27 14:20:45.998 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 离休人员工资表 的配置，使用智能默认处理
2025-06-27 14:20:45.999 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-27 14:20:46.003 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 16 个字段
2025-06-27 14:20:46.005 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 离休人员工资表 模板生成字段映射
2025-06-27 14:20:46.009 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_04_retired_employees
2025-06-27 14:20:46.010 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_04_retired_employees 生成标准化字段映射: 16 个字段
2025-06-27 14:20:46.010 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet 离休人员工资表 存在 1 个验证错误
2025-06-27 14:20:46.015 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-06-27 14:20:46.016 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名'] -> ['employee_id', 'employee_name']
2025-06-27 14:20:46.017 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_04_retired_employees 不存在，将根据模板创建...
2025-06-27 14:20:46.035 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_04_retired_employees 保存 2 条数据。
2025-06-27 14:20:46.036 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 14:20:46.045 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 14:20:46.047 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 14:20:46.191 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 14行 x 21列
2025-06-27 14:20:46.195 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 14:20:46.195 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行14]: 姓名字段(姓名)为空, 数据: {'序号': '总计', '应发工资': np.float64(33607.14625)}
2025-06-27 14:20:46.196 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-06-27 14:20:46.196 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 退休人员工资表 的配置，使用智能默认处理
2025-06-27 14:20:46.197 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-06-27 14:20:46.198 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 27 个字段
2025-06-27 14:20:46.199 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 退休人员工资表 模板生成字段映射
2025-06-27 14:20:46.204 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_04_pension_employees
2025-06-27 14:20:46.204 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_04_pension_employees 生成标准化字段映射: 27 个字段
2025-06-27 14:20:46.205 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet 退休人员工资表 存在 1 个验证错误
2025-06-27 14:20:46.208 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-06-27 14:20:46.210 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['人员代码', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 14:20:46.211 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_04_pension_employees 不存在，将根据模板创建...
2025-06-27 14:20:46.230 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_04_pension_employees 保存 13 条数据。
2025-06-27 14:20:46.231 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 14:20:46.232 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 14:20:46.232 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 14:20:46.409 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 1397行 x 23列
2025-06-27 14:20:46.414 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 14:20:46.415 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行1397]: 姓名字段(姓名)为空, 数据: {'2025年岗位工资': np.float64(3971045.71), '2025年薪级工资': np.int64(3138129), '津贴': np.float64(94436.73000000001), '结余津贴': np.int64(78008), '2025年基础性绩效': np.int64(4706933), '卫生费': np.float64(14240.0), '交通补贴': np.float64(306460.0), '物业补贴': np.float64(305860.0), '住房补贴': np.float64(337019.8710385144), '车补': np.float64(12870.0), '通讯补贴': np.float64(10250.0), '2025年奖励性绩效预发': np.int64(2262100), '补发': np.float64(2528.0), '借支': np.float64(122740.12055172413), '应发工资': np.float64(15117140.190486807), '2025公积金': np.int64(2390358), '代扣代存养老保险': np.float64(1967911.5299999986)}
2025-06-27 14:20:46.418 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-06-27 14:20:46.419 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 全部在职人员工资表 的配置，使用智能默认处理
2025-06-27 14:20:46.421 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-06-27 14:20:46.423 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 23 个字段
2025-06-27 14:20:46.423 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 全部在职人员工资表 模板生成字段映射
2025-06-27 14:20:46.428 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_04_active_employees
2025-06-27 14:20:46.428 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_04_active_employees 生成标准化字段映射: 23 个字段
2025-06-27 14:20:46.429 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet 全部在职人员工资表 存在 2 个验证错误
2025-06-27 14:20:46.436 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-06-27 14:20:46.443 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 14:20:46.452 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_04_active_employees 不存在，将根据模板创建...
2025-06-27 14:20:46.497 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_04_active_employees 保存 1396 条数据。
2025-06-27 14:20:46.498 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-06-27 14:20:46.499 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-06-27 14:20:46.499 | INFO     | src.utils.log_config:log_file_operation:250 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-06-27 14:20:46.672 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:315 | 导入完成: 63行 x 17列
2025-06-27 14:20:46.675 | WARNING  | src.modules.data_import.excel_importer:_filter_invalid_data:525 | 数据导入过滤: 发现1条姓名为空的记录
2025-06-27 14:20:46.675 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:527 | 过滤记录[行63]: 姓名字段(姓名)为空, 数据: {'应发工资': np.float64(471980.9)}
2025-06-27 14:20:46.677 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:565 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-06-27 14:20:46.677 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:415 | 未找到工作表 A岗职工 的配置，使用智能默认处理
2025-06-27 14:20:46.679 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:691 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-06-27 14:20:46.682 | INFO     | src.modules.data_import.multi_sheet_importer:_adapt_template_to_excel_headers:1189 | 模板适配完成: 21 个字段
2025-06-27 14:20:46.682 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:439 | 使用 A岗职工 模板生成字段映射
2025-06-27 14:20:46.690 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:517 | 完整字段映射保存成功: salary_data_2027_04_a_grade_employees
2025-06-27 14:20:46.692 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:469 | 为表 salary_data_2027_04_a_grade_employees 生成标准化字段映射: 21 个字段
2025-06-27 14:20:46.751 | WARNING  | src.modules.data_import.multi_sheet_importer:_process_sheet_data:479 | Sheet A岗职工 存在 2 个验证错误
2025-06-27 14:20:46.776 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:488 | Sheet A岗职工 数据处理完成: 62 行
2025-06-27 14:20:46.779 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:840 | 列名映射成功: ['工号', '姓名', '津贴', '应发工资'] -> ['employee_id', 'employee_name', 'allowance', 'total_salary']
2025-06-27 14:20:46.786 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:853 | 表 salary_data_2027_04_a_grade_employees 不存在，将根据模板创建...
2025-06-27 14:20:46.865 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:951 | 成功向表 salary_data_2027_04_a_grade_employees 保存 62 条数据。
2025-06-27 14:20:46.941 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:224 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_04_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2027_04_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_04_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_04_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_04_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_04_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_04_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_04_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-06-27 14:20:46.959 | INFO     | src.gui.dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_04_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2027_04_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_04_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_04_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_04_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_04_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_04_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_04_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2027-04', 'data_description': '2027年4月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2027年 > 4月 > 全部在职人员'}
2025-06-27 14:20:46.964 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2460 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_04_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2027_04_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_04_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2027_04_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2027_04_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2027_04_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2027_04_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2027_04_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 6}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2027-04', 'data_description': '2027年4月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2027年 > 4月 > 全部在职人员'}
2025-06-27 14:20:46.967 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2471 | 导入模式: multi_sheet, 目标路径: '工资表 > 2027年 > 4月 > 全部在职人员'
2025-06-27 14:20:46.968 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:2479 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2027年 > 4月 > 全部在职人员
2025-06-27 14:20:46.968 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2537 | 检查是否需要更新导航面板: ['工资表', '2027年', '4月', '全部在职人员']
2025-06-27 14:20:46.969 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2541 | 检测到工资数据导入，开始刷新导航面板
2025-06-27 14:20:46.978 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2545 | 使用强制刷新方法
2025-06-27 14:20:46.978 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1230 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-06-27 14:20:46.984 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1238 | 找到工资表节点: 📊 工资表
2025-06-27 14:20:46.987 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 3月 > 全部在职人员', '工资表 > 2027年 > 3月 > A岗职工', '工资表 > 2027年 > 3月', '工资表 > 2027年 > 3月 > 离休人员', '工资表 > 2027年 > 3月 > 退休人员']
2025-06-27 14:20:46.988 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 3月 > 离休人员
2025-06-27 14:20:46.996 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2128019538432 已经注册，将覆盖现有注册
2025-06-27 14:20:47.012 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2128019333216 已经注册，将覆盖现有注册
2025-06-27 14:20:47.014 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 4 个表格到表头管理器
2025-06-27 14:20:47.015 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 14:20:47.015 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.58ms
2025-06-27 14:20:47.017 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 14:20:47.019 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:20:47.027 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2781 | 数据量小(2条)，使用普通模式
2025-06-27 14:20:47.028 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 3月 > 离休人员
2025-06-27 14:20:47.042 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_03_retired_employees 获取数据...
2025-06-27 14:20:47.052 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_03_retired_employees 获取 2 行数据。
2025-06-27 14:20:47.053 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2657 | 表 salary_data_2027_03_retired_employees 没有字段映射配置
2025-06-27 14:20:47.054 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:20:47.055 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_03_retired_employees 无字段偏好设置，显示所有字段
2025-06-27 14:20:47.113 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:702 | 找到 112 个匹配类型 'salary_data' 的表
2025-06-27 14:20:47.140 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2027年，包含 4 个月份
2025-06-27 14:20:47.141 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2026年，包含 9 个月份
2025-06-27 14:20:47.142 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2025年，包含 6 个月份
2025-06-27 14:20:47.144 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2024年，包含 1 个月份
2025-06-27 14:20:47.145 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2023年，包含 1 个月份
2025-06-27 14:20:47.146 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2022年，包含 1 个月份
2025-06-27 14:20:47.150 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2021年，包含 1 个月份
2025-06-27 14:20:47.150 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2020年，包含 1 个月份
2025-06-27 14:20:47.151 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2019年，包含 1 个月份
2025-06-27 14:20:47.152 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2018年，包含 1 个月份
2025-06-27 14:20:47.153 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2017年，包含 1 个月份
2025-06-27 14:20:47.153 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1323 | 创建年份节点: 2016年，包含 1 个月份
2025-06-27 14:20:47.154 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1343 | 工资数据导航已强制刷新: 12 个年份, 28 个月份
2025-06-27 14:20:47.155 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1346 | 默认路径: 工资表 > 2027年 > 1月 > 全部在职人员
2025-06-27 14:20:47.156 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1385 | force_refresh_salary_data 执行完成
2025-06-27 14:20:47.156 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:2550 | 将在800ms后导航到: 工资表 > 2027年 > 4月 > 全部在职人员
2025-06-27 14:20:47.214 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-27 14:20:47.237 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 14:20:47.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 2 -> 2
2025-06-27 14:20:47.239 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 14:20:47.240 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-27 14:20:47.263 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 41.20ms
2025-06-27 14:20:47.311 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-27 14:20:47.958 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2583 | 尝试导航到新导入的路径: 工资表 > 2027年 > 4月 > 全部在职人员
2025-06-27 14:20:47.960 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 3月 > 全部在职人员', '工资表 > 2027年 > 3月 > A岗职工', '工资表 > 2027年 > 3月', '工资表 > 2027年 > 4月 > 全部在职人员', '工资表 > 2027年 > 3月 > 离休人员']
2025-06-27 14:20:47.969 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 4月 > 全部在职人员
2025-06-27 14:20:47.975 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2128019538432 已经注册，将覆盖现有注册
2025-06-27 14:20:47.979 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2128019333216 已经注册，将覆盖现有注册
2025-06-27 14:20:47.979 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2126107305328 已经注册，将覆盖现有注册
2025-06-27 14:20:47.983 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2126107206016 已经注册，将覆盖现有注册
2025-06-27 14:20:47.984 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 4 个表格到表头管理器
2025-06-27 14:20:47.985 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 14:20:47.987 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 2.06ms
2025-06-27 14:20:47.987 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 14:20:47.988 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:20:47.989 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2778 | 数据量大(1396条)，启用分页模式
2025-06-27 14:20:47.992 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2809 | 使用分页模式加载 salary_data_2027_04_active_employees，第1页，每页50条
2025-06-27 14:20:47.993 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:2852 | 缓存未命中，从数据库加载: salary_data_2027_04_active_employees 第1页
2025-06-27 14:20:47.994 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月 > 全部在职人员
2025-06-27 14:20:47.994 | INFO     | src.gui.prototype.prototype_main_window:run:123 | 开始加载表 salary_data_2027_04_active_employees 第1页数据，每页50条
2025-06-27 14:20:47.995 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_active_employees 分页获取数据: 第1页, 每页50条
2025-06-27 14:20:47.997 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:2588 | 已成功导航到新导入的路径: 工资表 > 2027年 > 4月 > 全部在职人员
2025-06-27 14:20:48.007 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_active_employees 获取第1页数据: 50 行，总计1396行
2025-06-27 14:20:48.063 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 14:20:48.069 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2670 | 字段映射应用成功: 5 个字段重命名
2025-06-27 14:20:48.071 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 成功加载 50 条数据，16 个字段，总记录数: 1396
2025-06-27 14:20:48.077 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:2882 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-06-27 14:20:48.091 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 14:20:48.093 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:20:48.094 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_04_active_employees 无字段偏好设置，显示所有字段
2025-06-27 14:20:48.094 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 50 行
2025-06-27 14:20:48.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 2 -> 50
2025-06-27 14:20:48.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 14:20:48.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 14:20:48.116 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 14:20:48.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 14:20:48.154 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 55.56ms
2025-06-27 14:20:48.156 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 50
2025-06-27 14:20:48.161 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 1396
2025-06-27 14:21:00.354 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 14:21:00.354 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:21:00.354 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 14:21:00.354 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_active_employees 分页获取数据: 第2页, 每页50条
2025-06-27 14:21:00.354 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_active_employees 获取第2页数据: 50 行，总计1396行
2025-06-27 14:21:00.372 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 14:21:00.372 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 14:21:00.372 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 14:21:00.372 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 14:21:00.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 31.80ms
2025-06-27 14:21:00.386 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第2页, 显示50条记录，字段数: 16
2025-06-27 14:21:00.386 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 2
2025-06-27 14:21:04.880 | WARNING  | src.gui.prototype.prototype_main_window:_on_page_changed:545 | 主窗口不支持分页数据加载，回退到本地加载
2025-06-27 14:21:04.880 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:21:04.896 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:551 | 统一分页加载所有字段（修复字段不一致问题）
2025-06-27 14:21:04.896 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1016 | 正在从表 salary_data_2027_04_active_employees 分页获取数据: 第3页, 每页50条
2025-06-27 14:21:04.896 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:1039 | 成功从表 salary_data_2027_04_active_employees 获取第3页数据: 50 行，总计1396行
2025-06-27 14:21:04.896 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 50
2025-06-27 14:21:04.896 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(50)自动调整最大可见行数为: 50
2025-06-27 14:21:04.896 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 14:21:04.896 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 50 行, 16 列
2025-06-27 14:21:04.930 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 50 行, 最大可见行数: 50, 耗时: 34.75ms
2025-06-27 14:21:04.934 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed:575 | 本地页码变化处理完成: 第3页, 显示50条记录，字段数: 16
2025-06-27 14:21:04.934 | INFO     | src.gui.widgets.pagination_widget:set_current_page:245 | 页码切换到: 3
2025-06-27 14:21:07.083 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 3月 > 全部在职人员', '工资表 > 2027年 > 3月 > A岗职工', '工资表 > 2027年 > 3月', '工资表 > 2027年 > 4月 > 全部在职人员', '工资表 > 2027年']
2025-06-27 14:21:07.083 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 4月 > 退休人员
2025-06-27 14:21:07.098 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2128019538432 已经注册，将覆盖现有注册
2025-06-27 14:21:07.098 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2128019333216 已经注册，将覆盖现有注册
2025-06-27 14:21:07.098 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2126107305328 已经注册，将覆盖现有注册
2025-06-27 14:21:07.098 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2126107206016 已经注册，将覆盖现有注册
2025-06-27 14:21:07.098 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 4 个表格到表头管理器
2025-06-27 14:21:07.098 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 14:21:07.098 | WARNING  | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:507 | 检测到表格 table_1_2128019333216 的表头重影: ['id', '工号']
2025-06-27 14:21:07.098 | WARNING  | src.gui.table_header_manager:_clear_single_table_header:196 | 检测到表格 table_1_2128019333216 存在表头重影: ['id', '工号']
2025-06-27 14:21:07.098 | WARNING  | src.gui.prototype.prototype_main_window:_on_header_shadow_detected:3461 | 检测到表格 table_1_2128019333216 表头重影: ['id', '工号']
2025-06-27 14:21:07.309 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:519 | 表格 table_1_2128019333216 表头重影修复成功
2025-06-27 14:21:07.310 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 211.63ms
2025-06-27 14:21:07.311 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：1 个重影表格，1 个修复成功，0 个修复失败
2025-06-27 14:21:07.311 | INFO     | src.gui.prototype.prototype_main_window:_pre_clear_headers_on_navigation_change:3385 | 导航切换预清理：修复了 1 个表格的表头重影
2025-06-27 14:21:07.312 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:21:07.313 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2781 | 数据量小(13条)，使用普通模式
2025-06-27 14:21:07.314 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月 > 退休人员
2025-06-27 14:21:07.314 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_04_pension_employees 获取数据...
2025-06-27 14:21:07.316 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_04_pension_employees 获取 13 行数据。
2025-06-27 14:21:07.318 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2670 | 字段映射应用成功: 5 个字段重命名
2025-06-27 14:21:07.319 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:21:07.322 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_04_pension_employees 无字段偏好设置，显示所有字段
2025-06-27 14:21:07.332 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 13 行
2025-06-27 14:21:07.359 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 50 -> 13
2025-06-27 14:21:07.401 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(13)自动调整最大可见行数为: 13
2025-06-27 14:21:07.403 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 14:21:07.404 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 13 行, 16 列
2025-06-27 14:21:07.437 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 13 行, 最大可见行数: 13, 耗时: 98.50ms
2025-06-27 14:21:07.471 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 13
2025-06-27 14:21:09.036 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2027年 > 4月 > 全部在职人员', '工资表 > 2027年 > 3月 > 全部在职人员', '工资表 > 2027年 > 3月 > A岗职工', '工资表 > 2027年 > 3月', '工资表 > 2027年']
2025-06-27 14:21:09.036 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:2720 | 导航变化: 工资表 > 2027年 > 4月 > 离休人员
2025-06-27 14:21:09.052 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_0_2128019538432 已经注册，将覆盖现有注册
2025-06-27 14:21:09.052 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_1_2128019333216 已经注册，将覆盖现有注册
2025-06-27 14:21:09.052 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_2_2126107305328 已经注册，将覆盖现有注册
2025-06-27 14:21:09.052 | WARNING  | src.gui.table_header_manager:register_table:83 | 表格 table_3_2126107206016 已经注册，将覆盖现有注册
2025-06-27 14:21:09.052 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:3302 | 已注册 4 个表格到表头管理器
2025-06-27 14:21:09.052 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:490 | 开始增强版表头重影检测，共 4 个表格
2025-06-27 14:21:09.052 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:552 | 增强版表头重影检测完成，耗时 0.00ms
2025-06-27 14:21:09.052 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:553 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-06-27 14:21:09.052 | INFO     | src.gui.widgets.pagination_widget:reset:269 | 分页状态已重置
2025-06-27 14:21:09.052 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:2781 | 数据量小(2条)，使用普通模式
2025-06-27 14:21:09.052 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:811 | 导航选择: 工资表 > 2027年 > 4月 > 离休人员
2025-06-27 14:21:09.052 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:984 | 正在从表 salary_data_2027_04_retired_employees 获取数据...
2025-06-27 14:21:09.052 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_from_table:995 | 成功从表 salary_data_2027_04_retired_employees 获取 2 行数据。
2025-06-27 14:21:09.052 | INFO     | src.gui.prototype.prototype_main_window:_apply_field_mapping_to_dataframe:2670 | 字段映射应用成功: 3 个字段重命名
2025-06-27 14:21:09.067 | INFO     | src.modules.data_import.config_sync_manager:__init__:66 | 配置同步管理器初始化完成
2025-06-27 14:21:09.067 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:2710 | 表 salary_data_2027_04_retired_employees 无字段偏好设置，显示所有字段
2025-06-27 14:21:09.083 | INFO     | src.gui.prototype.prototype_main_window:set_data:467 | 通过公共接口设置新的表格数据: 2 行
2025-06-27 14:21:09.136 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2690 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-06-27 14:21:09.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:2699 | 最大可见行数已更新: 13 -> 2
2025-06-27 14:21:09.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:2752 | 根据数据量(2)自动调整最大可见行数为: 2
2025-06-27 14:21:09.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_apply_field_mapping:2060 | 表头映射应用完成: 16 个表头
2025-06-27 14:21:09.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:402 | 表格数据已设置: 2 行, 16 列
2025-06-27 14:21:09.156 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2005 | 表格数据设置完成: 2 行, 最大可见行数: 2, 耗时: 31.74ms
2025-06-27 14:21:09.157 | INFO     | src.gui.widgets.pagination_widget:set_total_records:231 | 总记录数设置为: 2
2025-06-27 14:21:12.728 | INFO     | __main__:main:302 | 应用程序正常退出
