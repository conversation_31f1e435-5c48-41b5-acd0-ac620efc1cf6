<!-- Generated by SpecStory -->

# 处理字段映射中的持续问题 (2025-06-27 10:54:41)

_**User**_

按照 @多次修改依然无法根本解决的问题-字段映射.md 中内容处理

---

_**Assistant**_



---

我将按照文档要求，仔细分析项目现状，梳理字段映射问题的根本原因，并给出完整的解决方案。

---

<details>
            <summary>Searched codebase "字段映射 field mapping 表头 header" • **22** results</summary>
        
| File | Lines |
|------|-------|
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L2004-2041 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L4155-4200 |
| `src/gui/prototype/prototype_main_window.py` | L2328-2367 |
| `docs/fields_map/工资表表头映射与自定义显示解决方案.md` | L117-167 |
| `docs/fields_map/工资表表头映射与自定义显示解决方案.md` | L167-216 |
| `docs/fields_map/表头英文显示问题完整解决方案.md` | L1-56 |
| `docs/fields_map/分页字段映射丢失问题分析与解决方案.md` | L267-307 |
| `docs/fields_map/工资表表头映射与自定义显示解决方案.md` | L37-88 |
| `docs/fields_map/分页字段映射丢失问题分析与解决方案.md` | L105-147 |
| `docs/fields_map/字段映射机制深度解析与优化方案.md` | L191-233 |
| `src/modules/data_import/auto_field_mapping_generator.py` | L651-663 |
| `temp/test_phase1_field_mapping_integration.py` | L718-750 |
| `docs/fields_map/工资表表头映射与自定义显示解决方案.md` | L255-313 |
| `src/modules/data_import/auto_field_mapping_generator.py` | L609-651 |
| `docs/fields_map/数据导入字段映射根本问题分析与解决方案.md` | L209-242 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L3924-3970 |
| `src/gui/prototype/widgets/virtualized_expandable_table.py` | L3990-4018 |
| `src/modules/data_import/auto_field_mapping_generator.py` | L114-135 |
| `temp/diagnose_header_mapping.py` | L70-106 |
| `src/modules/data_import/field_mapper.py` | L1-61 |
| `temp/test_header_mapping_quick.py` | L1-56 |
| `temp/diagnose_header_mapping.py` | L106-158 |

</details>

---

<details>
            <summary>Grep search for "field.*map" • **19** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/temp/auto_generate_field_mapping.py` | Lundefined | `⟪ 296 characters skipped ⟫t(0, str(project_root))\n\ndef generate_field_mapping_for_new_tables():\n    \"\"\"为新导入的表生成字段映射\"\"\"\n    print(\"=\" * 60)\n    print(\"自动生成字段映射配置\")\n    print(\"=\" * 60)\n    \n    # 1. 读取现有字段映射\n    field_mappings_file = project_root` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_complete_functionality.py` | L31 | `from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_complete_functionality.py` | L43 | `auto_mapping = AutoFieldMappingGenerator()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L63 | `field_mapping = config_sync.get_mapping(table_name)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L64 | `has_mapping = field_mapping is not None and len(field_mapping) > 0` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L69 | `print(f"    映射字段数: {len(field_mapping)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L70 | `print(f"    前3个映射: {dict(list(field_mapping.items())[:3])}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/verify_pagination_fix_status.py` | L139 | `print("✅ _apply_field_mapping_to_dataframe() - 在显示层应用字段映射")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_ui.py` | L64 | `field_mappings = config_sync.get_mapping(test_table)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_ui.py` | L65 | `if field_mappings:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_ui.py` | L66 | `print(f"   映射数量: {len(field_mappings)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_ui.py` | L67 | `print(f"   前3个映射: {dict(list(field_mappings.items())[:3])}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_ui.py` | L70 | `field_mappings = {}` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_table_field_preference_ui.py` | L86 | `field_mappings=field_mappings` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/diagnose_header_mapping.py` | L28 | `"src/modules/data_import/auto_field_mapping_generator.py",` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/diagnose_header_mapping.py` | L54 | `("AutoFieldMappingGenerator", "src.modules.data_import.auto_field_mapping_generator"),` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/diagnose_header_mapping.py` | L77 | `config_file = project_root / "config" / "field_mappings.json"` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/diagnose_header_mapping.py` | L115 | `from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/diagnose_header_mapping.py` | L122 | `auto_mapping = AutoFieldMappingGenerator()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/diagnose_header_mapping.py` | L123 | `print("✅ AutoFieldMappingGenerator 初始化成功")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L66 | `field_mapping = config_sync.get_mapping(table_name)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L67 | `if field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L68 | `print(f"      有字段映射: {len(field_mapping)}个映射")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L70 | `df_mapped = apply_field_mapping_simulation(df_page1, field_mapping)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L82 | `available_fields = [field for field in table_preference if field in df_mapped.columns]` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L97 | `df_page2_mapped = apply_field_mapping_simulation(df_page2, field_mapping) if field_mapping else df_page2` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L100 | `available_fields = [field for field in table_preference if field in df_page2_mapped.columns]` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L146 | `def apply_field_mapping_simulation(df: pd.DataFrame, field_mapping: dict) -> pd.DataFrame:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L148 | `if not field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix_integration.py` | L157 | `for excel_col, chinese_name in field_mapping.items():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix.py` | L66 | `field_mapping = config_sync.get_mapping(test_table)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix.py` | L67 | `has_mapping = field_mapping is not None and len(field_mapping) > 0` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix.py` | L71 | `print(f"  映射字段数: {len(field_mapping)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix.py` | L122 | `if col in field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_fix.py` | L123 | `mapped_columns.append(field_mapping[col])` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L193 | `def test_field_mapping_consistency(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L223 | `mapped_page1 = main_window._apply_field_mapping_to_dataframe(page1_data, self.test_table)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L224 | `mapped_page2 = main_window._apply_field_mapping_to_dataframe(page2_data, self.test_table)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L227 | `mapped_fields1 = list(mapped_page1.columns)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L228 | `mapped_fields2 = list(mapped_page2.columns)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L230 | `self.logger.info(f"映射后第1页字段: {mapped_fields1} (共{len(mapped_fields1)}个)")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L231 | `self.logger.info(f"映射后第2页字段: {mapped_fields2} (共{len(mapped_fields2)}个)")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L233 | `if mapped_fields1 == mapped_fields2:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_pagination_field_consistency.py` | L301 | `test_results["field_mapping_consistency"] = self.test_field_mapping_consistency()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_header_mapping_quick.py` | L19 | `from src.modules.data_import.auto_field_mapping_generator import AutoFieldMappingGenerator` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_header_mapping_quick.py` | L21 | `def test_field_mapping():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_header_mapping_quick.py` | L29 | `auto_mapping = AutoFieldMappingGenerator()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_header_mapping_quick.py` | L58 | `test_field_mapping()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L33 | `class FieldMappingFixTester:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L46 | `self.field_mappings_file = project_root / "state" / "data" / "field_mappings.json"` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L64 | `self.test_3_excel_import_field_mapping()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L88 | `if self.field_mappings_file.exists():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L89 | `with open(self.field_mappings_file, 'r', encoding='utf-8') as f:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L143 | `def test_3_excel_import_field_mapping(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L165 | `if self.field_mappings_file.exists():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L166 | `with open(self.field_mappings_file, 'r', encoding='utf-8') as f:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L192 | `if not self.field_mappings_file.exists():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L196 | `with open(self.field_mappings_file, 'r', encoding='utf-8') as f:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L250 | `if self.field_mappings_file.exists():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L251 | `with open(self.field_mappings_file, 'r', encoding='utf-8') as f:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L343 | `report_file = project_root / "temp" / "field_mapping_fix_test_report.md"` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_field_mapping_fix_complete.py` | L351 | `tester = FieldMappingFixTester()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/table_field_preference_dialog.py` | L40 | `field_mappings: Dict[str, str] = None, parent=None):` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/table_field_preference_dialog.py` | L47 | `field_mappings: 字段映射 {db_field: display_name}` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/table_field_preference_dialog.py` | L55 | `self.field_mappings = field_mappings or {}` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/table_field_preference_dialog.py` | L157 | `display_name = self.field_mappings.get(field, field)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/table_field_preference_dialog.py` | L294 | `display_name = self.field_mappings.get(field, field)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/table_field_preference_dialog.py` | L344 | `display_name = self.field_mappings.get(field, field)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/table_field_preference_dialog.py` | L524 | `test_table_name, test_fields, test_mappings` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_auto_mapping_phase1.py` | L20 | `from src.modules.data_import import AutoFieldMappingGenerator, ConfigSyncManager` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/test_auto_mapping_phase1.py` | L32 | `generator = AutoFieldMappingGenerator()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/simple_debug_header.py` | L24 | `field_mappings_file = project_root / "state" / "data" / "field_mappings.json"` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/simple_debug_header.py` | L25 | `print(f"字段映射文件：{field_mappings_file}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/simple_debug_header.py` | L26 | `print(f"文件存在：{field_mappings_file.exists()}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/simple_debug_header.py` | L28 | `if not field_mappings_file.exists():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/simple_debug_header.py` | L34 | `with open(field_mappings_file, 'r', encoding='utf-8') as f:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/simple_debug_header.py` | L44 | `field_mapping = table_mappings[table_name].get('field_mappings', {})` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/simple_debug_header.py` | L46 | `print(f"字段映射：{field_mapping}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/simple_debug_header.py` | L49 | `for eng_field, chn_field in field_mapping.items():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/safe_config_cleanup.py` | L13 | `config_path = "state/data/field_mappings.json"` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/safe_config_cleanup.py` | L15 | `backup_path = f"state/data/backups/field_mappings_before_cleanup_{timestamp}.json"` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/safe_config_cleanup.py` | L25 | `config_path = "state/data/field_mappings.json"` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/pagination_field_consistency_test_report.json` | L16 | `"field_mapping_consistency": true` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/main_window_field_mapping_fix.py` | L3 | `def _apply_field_mapping_to_dataframe_fixed(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/main_window_field_mapping_fix.py` | L8 | `field_mapping = self.config_sync_manager.load_mapping(table_name)` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/main_window_field_mapping_fix.py` | L10 | `field_mapping = self.field_mappings.get(table_name, {})` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/main_window_field_mapping_fix.py` | L12 | `if not field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/main_window_field_mapping_fix.py` | L24 | `if db_col in field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/main_window_field_mapping_fix.py` | L25 | `display_name = field_mapping[db_col]` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/main_window_field_mapping_fix.py` | L44 | `# 1. 将上述代码中的 _apply_field_mapping_to_dataframe_fixed 方法` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/main_window_field_mapping_fix.py` | L45 | `# 2. 替换原文件中的 _apply_field_mapping_to_dataframe 方法` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/fix_field_mapping_structure.py` | L10 | `def fix_field_mapping_structure():` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/fix_field_mapping_structure.py` | L13 | `field_mappings_path = "state/data/field_mappings.json"` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/fix_field_mapping_structure.py` | L14 | `backup_path = f"state/data/field_mappings_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/fix_field_mapping_structure.py` | L16 | `print(f"开始修复字段映射配置文件结构: {field_mappings_path}")` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/fix_field_mapping_structure.py` | L20 | `with open(field_mappings_path, 'r', encoding='utf-8') as f:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/fix_field_mapping_structure.py` | L69 | `field_count = len(table_mappings[table_name].get('field_mappings', {}))` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/fix_field_mapping_structure.py` | L79 | `with open(field_mappings_path, 'w', encoding='utf-8') as f:` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/fix_field_mapping_structure.py` | L92 | `success = fix_field_mapping_structure()` |
| `file:///c%3A/test/salary_changes/salary_changes/temp/fix_field_mapping_system.py` | L8 | `1. 主界面的_apply_field_mapping_to_dataframe方法逻辑有缺陷` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L67 | `def _apply_field_mapping_to_dataframe(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L69 | `field_mapping = self.config_sync_manager.load_mapping(table_name)` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L71 | `# 问题：field_mapping的键是Excel列名，但df.columns是数据库字段名` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L73 | `if db_col in field_mapping:  # 匹配失败` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L115 | `"field_mappings": {` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L159 | `initial_mapping = self._create_initial_field_mapping(excel_headers, db_fields)` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L166 | `final_mapping = self._merge_field_mappings(existing_mapping, initial_mapping)` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L172 | `"field_mappings": final_mapping,` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L188 | `def _create_initial_field_mapping(self, excel_headers: List[str], db_fields: List[str]) -> Dict[str, str]:` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L215 | `if db_field not in mapping and i < len(excel_headers):` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L220 | `if db_field not in mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L231 | `def _apply_field_mapping_to_dataframe(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L235 | `field_mapping = self.config_sync_manager.load_mapping(table_name)` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L237 | `if not field_mapping:` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L244 | `for db_field, display_name in field_mapping.items():` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L289 | `success = self._update_field_mapping(db_field_name, new_display_name.strip())` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L306 | `def _update_field_mapping(self, db_field_name: str, new_display_name: str) -> bool:` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L310 | `return self.config_sync_manager.update_single_field_mapping(` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L324 | `def update_single_field_mapping(self, table_name: str, db_field: str, display_name: str) -> bool:` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L334 | `config["table_mappings"][table_name]["field_mappings"][db_field] = display_name` |

</details>

---

<details>
            <summary>Grep search for "表头|header" • **17** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/test/salary_changes/salary_changes/test/verify_field_mapping_fix.py` | L243 | `print("用户现在可以享受一致、可靠的中文表头显示体验。")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L42 | `"name": "场景1：英文表头表的分页浏览",` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L44 | `"description": "用户浏览无字段映射的英文表头表",` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L48 | `"name": "场景2：中文表头表的分页浏览",` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L50 | `"description": "用户浏览有字段映射的中文表头表",` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L182 | `print("   • 英文表头表的分页浏览")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L183 | `print("   • 中文表头表的分页浏览")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_real_world_pagination_scenarios.py` | L197 | `print("   • 中文表头正确显示")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_table_editing.py` | L108 | `sample_data, headers = create_sample_data(5)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_table_editing.py` | L109 | `table.set_data(sample_data, headers)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_table_editing.py` | L220 | `sample_data, headers = create_sample_data(10)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_table_editing.py` | L221 | `table.set_data(sample_data, headers)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_keyboard_shortcuts.py` | L176 | `data, headers = create_sample_data(15)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_keyboard_shortcuts.py` | L177 | `self.table.set_data(data, headers)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_drag_sort.py` | L665 | `data, headers = create_sample_data(20)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L152 | `• 观察空白区域和表头的菜单` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L185 | `simulate_header_btn = QPushButton("模拟表头右键")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L186 | `simulate_header_btn.clicked.connect(lambda: self.simulate_context_menu("header"))` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L187 | `self.simulate_buttons_layout.addWidget(simulate_header_btn)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L270 | `data, headers = create_sample_data(20)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L271 | `self.table.set_data(data, headers)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L278 | `"HEADER - 表头菜单",` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L290 | `elif "表头" in menu_type or "空白" in menu_type:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L387 | `(QPoint(50, 20), "表头区域"),` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L664 | `elif menu_type == "header":` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_context_menu.py` | L665 | `# 模拟表头右键` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_batch_operations.py` | L50 | `sample_data, headers = create_sample_data(10)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_batch_operations.py` | L51 | `table.set_data(sample_data, headers)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_batch_operations.py` | L125 | `sample_data, headers = create_sample_data(8)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_batch_operations.py` | L126 | `table.set_data(sample_data, headers)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_batch_operations.py` | L229 | `sample_data, headers = create_sample_data(15)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_batch_operations.py` | L230 | `table.set_data(sample_data, headers)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase4_batch_operations.py` | L282 | `f"已导出{len(data['rows'])}行数据\\n包含{len(data['headers'])}列")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase3_quick_verify.py` | L37 | `table.setHorizontalHeaderLabels(["姓名", "部门", "职位"])` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase3_enhanced_search.py` | L73 | `self.column_headers = ["姓名", "工号", "部门", "职位", "工资", "入职时间"]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase3_enhanced_search.py` | L111 | `self.table = QTableWidget(len(self.test_data), len(self.column_headers))` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_phase3_enhanced_search.py` | L112 | `self.table.setHorizontalHeaderLabels(self.column_headers)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L5 | `1. 中文表头和英文表头的分页一致性` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L43 | `"salary_data_2026_01_a_grade_employees": "英文表头表（无映射）",` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_pagination_fix_comprehensive.py` | L44 | `"salary_data_2026_04_a_grade_employees": "中文表头表（有映射）"` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L58 | `data, headers = create_sample_data(1500)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_max_visible_rows.py` | L59 | `table2.set_data(data, headers, auto_adjust_visible_rows=True)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_large_data_optimizer.py` | L158 | `headers = ["姓名", "工号", "部门", "工资"]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_large_data_optimizer.py` | L159 | `self.table = VirtualizedTableWidget(headers=headers)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_large_data_optimizer.py` | L320 | `headers = ["姓名", "工号", "部门", "工资"]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_large_data_optimizer.py` | L321 | `table = VirtualizedTableWidget(headers=headers)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_import_dialog_fix.py` | L89 | `imported_data['headers']` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_import_defaults.py` | L47 | `self.assertTrue(settings['include_header'])` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_import_defaults.py` | L71 | `self.assertTrue(loaded_settings['include_header'])` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L3 | `表头重影修复效果测试脚本` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L5 | `测试三层防护机制是否有效防止表头重影问题：` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L8 | `3. 第三层防护：表头管理器的智能检测` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L10 | `作者: 表头重影修复项目组` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L39 | `class HeaderShadowFixTester:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L40 | `"""表头重影修复测试器"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L73 | `"""测试导航切换时的表头重影修复"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L74 | `logger.info("开始测试导航切换时的表头重影修复...")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L100 | `# 检查表头状态` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L101 | `if self.check_header_state():` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L103 | `logger.info(f"测试 {test_count}: 成功 - 无表头重影")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L105 | `logger.warning(f"测试 {test_count}: 失败 - 检测到表头重影")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L126 | `def check_header_state(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L127 | `"""检查表头状态是否正常（无重影）"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L138 | `# 获取表头标签` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L139 | `headers = []` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L141 | `header_item = table.horizontalHeaderItem(i)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L142 | `if header_item:` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L143 | `headers.append(header_item.text())` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L145 | `# 检查是否有重复的表头` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L146 | `unique_headers = set(headers)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L147 | `has_duplicates = len(headers) != len(unique_headers)` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L150 | `duplicates = [h for h in headers if headers.count(h) > 1]` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L151 | `logger.warning(f"检测到重复表头: {set(duplicates)}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L157 | `logger.error(f"检查表头状态失败: {e}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L160 | `def test_header_manager_detection(self):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L161 | `"""测试表头管理器的检测功能"""` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L162 | `logger.info("开始测试表头管理器的检测功能...")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L165 | `if not hasattr(self.main_window, 'header_manager'):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L166 | `logger.warning("表头管理器不存在")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L169 | `header_manager = self.main_window.header_manager` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L172 | `self.main_window._register_all_tables_to_header_manager()` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L175 | `if hasattr(header_manager, 'enhanced_auto_detect_and_fix_shadows'):` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L176 | `report = header_manager.enhanced_auto_detect_and_fix_shadows()` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L178 | `logger.info(f"表头管理器检测报告:")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L185 | `'test_name': '表头管理器检测',` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L198 | `logger.error(f"表头管理器检测测试失败: {e}")` |
| `file:///c%3A/test/salary_changes/salary_changes/test/test_header_shadow_fix.py` | L203 | `logger.info("开始运行表头重影修复效果测试...")` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L7 | `- **第一页表现**：表头及字段数据显示正常（中文表头）` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L8 | `- **分页后表现**：跳转到下一页后，字段数据为空（主要是中文表头的字段）` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L9 | `- **期望效果**：分页前后字段映射应保持一致，中文表头始终显示` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L12 | `- 系统支持从Excel导入数据时生成字段映射（Excel表头→中文显示名）` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L13 | `- 系统支持用户双击表头修改字段显示名称` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L122 | `"original_excel_headers": {       // 保留原始Excel列名（用于审计）` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L149 | `# 获取Excel原始表头` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L150 | `excel_headers = df.columns.tolist()` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L159 | `initial_mapping = self._create_initial_field_mapping(excel_headers, db_fields)` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L173 | `"original_excel_headers": dict(zip(db_fields, excel_headers)),` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L188 | `def _create_initial_field_mapping(self, excel_headers: List[str], db_fields: List[str]) -> Dict[str, str]:` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L208 | `for excel_header in excel_headers:` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L209 | `if excel_header in patterns:` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L210 | `mapping[db_field] = excel_header` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L215 | `if db_field not in mapping and i < len(excel_headers):` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L216 | `mapping[db_field] = excel_headers[i]` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L263 | `### 4. 表头双击编辑功能增强` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L266 | `def _on_header_double_clicked(self, logical_index: int):` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L267 | `"""处理表头双击事件 - 支持用户自定义显示名"""` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L269 | `# 获取当前显示的表头名称` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L270 | `current_display_name = self.horizontalHeaderItem(logical_index).text()` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L282 | `"编辑表头",` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L292 | `# 更新表头显示` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L293 | `self.horizontalHeaderItem(logical_index).setText(new_display_name.strip())` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L296 | `self.header_edited.emit(logical_index, current_display_name, new_display_name.strip())` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L299 | `self._record_header_edit_history(db_field_name, current_display_name, new_display_name.strip())` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L301 | `self.logger.info(f"表头编辑成功: {db_field_name} -> {new_display_name.strip()}")` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L304 | `self.logger.error(f"处理表头双击编辑失败: {e}")` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L368 | `1. **完整支持用户编辑**：用户双击修改的表头名称会正确保存和应用` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L369 | `2. **分页一致性**：确保分页前后显示的表头名称完全一致` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L393 | `1. **增强表头双击编辑**：确保编辑结果正确保存` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L406 | `2. **双击编辑表头**：验证用户自定义名称的保存和应用` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L411 | `- 所有页面的表头显示完全一致` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L412 | `- 用户编辑的表头名称在分页时保持不变` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L418 | `这个方案通过统一字段映射的键值体系，从根本上解决了分页时中文表头丢失的问题。核心思路是将所有字段映射的键统一为数据库字段名，这样无论是从Excel导入、用户编辑还是分页查询，都能通过相同的键找到正确的显示名称。` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/fields_map/%E5%88%86%E9%A1%B5%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84%E4%B8%A2%E5%A4%B1%E9%97%AE%E9%A2%98%E5%88%86%E6%9E%90%E4%B8%8E%E8%A7%A3%E5%86%B3%E6%96%B9%E6%A1%88.md` | L420 | `该方案完全兼容现有的表头双击编辑功能，并增加了编辑历史跟踪，提升了系统的可维护性和用户体验。` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/problems/%E5%A4%9A%E6%AC%A1%E4%BF%AE%E6%94%B9%E4%BE%9D%E7%84%B6%E6%97%A0%E6%B3%95%E6%A0%B9%E6%9C%AC%E8%A7%A3%E5%86%B3%E7%9A%84%E9%97%AE%E9%A2%98-%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84.md` | L2 | `需要从excel文档中读取表头字段，然后跟数据库中相应表的字段进行"字段映射"，在主界面右侧列表展示区域表头部分显示自定义表头（或从excel文档中各个sheet表的中文表头）。` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/problems/%E5%A4%9A%E6%AC%A1%E4%BF%AE%E6%94%B9%E4%BE%9D%E7%84%B6%E6%97%A0%E6%B3%95%E6%A0%B9%E6%9C%AC%E8%A7%A3%E5%86%B3%E7%9A%84%E9%97%AE%E9%A2%98-%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84.md` | L7 | `### 当前主界面列表展示区域表头固定，无论哪类表，不符合实际情况` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/problems/%E5%A4%9A%E6%AC%A1%E4%BF%AE%E6%94%B9%E4%BE%9D%E7%84%B6%E6%97%A0%E6%B3%95%E6%A0%B9%E6%9C%AC%E8%A7%A3%E5%86%B3%E7%9A%84%E9%97%AE%E9%A2%98-%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84.md` | L9 | `### 当前主界面右侧列表展示区域表头显示的中文字段` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/problems/%E5%A4%9A%E6%AC%A1%E4%BF%AE%E6%94%B9%E4%BE%9D%E7%84%B6%E6%97%A0%E6%B3%95%E6%A0%B9%E6%9C%AC%E8%A7%A3%E5%86%B3%E7%9A%84%E9%97%AE%E9%A2%98-%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84.md` | L13 | `### 工资表（excel文档）中4类sheet表表头字段` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/problems/%E5%A4%9A%E6%AC%A1%E4%BF%AE%E6%94%B9%E4%BE%9D%E7%84%B6%E6%97%A0%E6%B3%95%E6%A0%B9%E6%9C%AC%E8%A7%A3%E5%86%B3%E7%9A%84%E9%97%AE%E9%A2%98-%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84.md` | L14 | `sheet表中表头字段如果有特殊字符，如空格、换行符等，需要清理后，再跟数据库相应表的字段进行"字段映射"。` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/problems/%E5%A4%9A%E6%AC%A1%E4%BF%AE%E6%94%B9%E4%BE%9D%E7%84%B6%E6%97%A0%E6%B3%95%E6%A0%B9%E6%9C%AC%E8%A7%A3%E5%86%B3%E7%9A%84%E9%97%AE%E9%A2%98-%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84.md` | L32 | `### 每个表表头数量不一定完全一样，而且，数据库表中有的字段，而sheet表头没有的字段如何处理？` |
| `file:///c%3A/test/salary_changes/salary_changes/docs/problems/%E5%A4%9A%E6%AC%A1%E4%BF%AE%E6%94%B9%E4%BE%9D%E7%84%B6%E6%97%A0%E6%B3%95%E6%A0%B9%E6%9C%AC%E8%A7%A3%E5%86%B3%E7%9A%84%E9%97%AE%E9%A2%98-%E5%AD%97%E6%AE%B5%E6%98%A0%E5%B0%84.md` | L35 | `### 有个疑问：在主界面右侧列表展示区域表头部分，通过双击某个表头修改显示名称，这个修改后的名称是如何保存的？` |

</details>

---

现在让我深入查看关键的字段映射相关代码文件，以全面了解当前项目状况：

---

Read file: src/modules/data_import/config_sync_manager.py

---

Read file: src/gui/prototype/widgets/virtualized_expandable_table.py

---

Read file: src/gui/prototype/prototype_main_window.py

---

现在让我查看一个实际的配置文件样例，了解当前的字段映射存储结构：

---

<details>
            <summary>Searched codebase "field_mappings.json" • **0** results</summary>
        
No results found
</details>

---

Could not find file 'state/data/field_mappings.json' in the workspace.

---

让我检查config目录下的字段映射文件：

---

<details>
            <summary>Listed directory state • **5** results</summary>
        
| Name |
|-------|
| 📁 `user` |
| 📁 `data` |
| 📁 `backups` |
| 📁 `sessions` |
| 📁 `windows` |

</details>

---

Could not resolve URI: Error: Unable to resolve nonexistent file 'c:\test\salary_changes\salary_changes\config'

---

<details>
            <summary>Searched codebase "config.json" • **3** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Read file: config.json

---

<details>
            <summary>Listed directory state/data • **1** results</summary>
        
| Name |
|-------|
| 📁 `backups` |

</details>

---

让我查看数据库文件和用户配置文件：

---

Read file: user_preferences.json

---

```bash
ls -la state/data/
```

```
版权所有 (C) Microsoft Corporation。保留所有权利。

加载个人及系统配置文件用了 3497 毫秒。

























                                                               ls -la state/data/
Get-ChildItem : 找不到与参数名称“la”匹配的参数。
所在位置 行:1 字符: 4
+ ls -la state/data/
+    ~~~
    + CategoryInfo          : InvalidArgument: (:) [Get-ChildItem]，ParameterBindingException
    + FullyQualifiedErrorId : NamedParameterNotFound,Microsoft.PowerShell.Commands.GetChildItemC 
   ommand
 
(salary_system_py39) PS C:\test\salary_changes\salary_changes>
```

---

```bash
dir state\data
```

```
(salary_system_py39) PS C:\test\salary_changes\salary_changes> dir state\data


    目录: C:\test\salary_changes\salary_changes\state\data


Mode                LastWriteTime         Length Name
----                -------------         ------ ----
d-----        2025/6/26     21:53                backups


(salary_system_py39) PS C:\test\salary_changes\salary_changes>
```

---



---

基于对项目代码的深入分析，我现在对字段映射问题有了全面的了解。让我按照文档要求，一步一步梳理项目当前状况，分析根本原因，并给出完整的解决方案。

## 📋 项目字段映射问题深度分析报告

### 🔍 一、项目当前状况梳理

#### 1.1 代码架构现状
项目已建立了完整的字段映射处理框架：

**核心组件：**
- `ConfigSyncManager`：字段映射配置持久化管理
- `AutoFieldMappingGenerator`：自动字段映射生成器
- `VirtualizedExpandableTable`：表格组件（支持字段映射应用）
- `PrototypeMainWindow`：主窗口（字段映射更新处理）

**配置结构：**
```json
{
  "table_mappings": {
    "salary_data_2025_05": {
      "metadata": {
        "created_at": "2025-06-26T10:00:00",
        "auto_generated": true,
        "user_modified": false
      },
      "field_mappings": {
        "employee_id": "工号",
        "employee_name": "姓名", 
        "department": "部门名称"
      },
      "edit_history": []
    }
  }
}
```

#### 1.2 当前实现功能
✅ **已实现：**
- 从Excel导入时自动生成字段映射
- 双击表头编辑显示名称
- 字段映射持久化存储
- 分页时字段映射应用

❌ **存在问题：**
- 表头显示固定化，无法按表类型动态切换
- 4类工资表（离休、退休、在职、A岗）表头结构差异无法适配
- 数据库字段与Excel表头的映射关系不一致

### 🎯 二、问题根本原因分析

#### 2.1 主要原因（核心矛盾）

**原因1：表头映射策略缺陷**
```python
# 当前问题代码（固定表头）
headers = ["工号", "姓名", "部门名称", "津贴", "应发工资"]  # 硬编码
```

**问题症结：**
- 系统对所有表类型使用相同表头模板
- 未根据不同工资表类型（离休/退休/在职/A岗）动态适配表头

**原因2：字段映射键值体系混乱**
```python
# 映射关系不一致问题
mapping = {
    "工号": "employee_id",    # Excel列名 → 数据库字段名
    "employee_id": "工号"    # 数据库字段名 → 显示名
}
```

**问题症结：**
- 映射的键（key）有时是Excel原始列名，有时是数据库字段名
- 导致分页时无法正确应用字段映射

#### 2.2 次要原因（衍生问题）

**原因3：Excel表头清理不充分**
- 4类工资表的表头存在特殊字符（空格、换行符）
- 字段映射生成时清理逻辑不完整

**原因4：数据库字段缺失处理不当**
- 当数据库有字段但Excel表头没有时，处理策略不明确
- 缺少默认显示名生成逻辑

### 🚀 三、完整解决方案

#### 3.1 方案概述

**核心思路：建立"表类型感知"的动态字段映射体系**

1. **表类型识别机制**：根据导入的Excel sheet名称自动识别表类型
2. **分层字段映射**：为不同表类型建立专用的字段映射模板
3. **智能字段适配**：处理字段数量不一致和缺失字段问题
4. **用户自定义增强**：支持用户按表类型个性化定制显示字段

#### 3.2 详细技术方案

**方案A：表类型感知的字段映射系统**

```mermaid
graph TD
    A[Excel导入] --> B[表类型识别]
    B --> C{表类型}
    C -->|离休人员工资表| D[离休表字段模板]
    C -->|退休人员工资表| E[退休表字段模板]
    C -->|全部在职人员工资表| F[在职表字段模板]
    C -->|A岗职工| G[A岗表字段模板]
    D --> H[生成专用字段映射]
    E --> H
    F --> H
    G --> H
    H --> I[应用到表头显示]
    I --> J[用户可双击编辑]
    J --> K[保存到对应表类型配置]
```

**1. 表类型识别器**
```python
class TableTypeIdentifier:
    TYPE_PATTERNS = {
        "retired_old": ["离休人员工资表", "离休"],
        "retired": ["退休人员工资表", "退休"],
        "active_all": ["全部在职人员工资表", "在职"],
        "grade_a": ["A岗职工", "A岗"]
    }
    
    def identify_table_type(self, sheet_name: str) -> str:
        """根据sheet名称识别表类型"""
        for table_type, patterns in self.TYPE_PATTERNS.items():
            if any(pattern in sheet_name for pattern in patterns):
                return table_type
        return "default"
```

**2. 分层字段映射模板**
```python
FIELD_MAPPING_TEMPLATES = {
    "retired_old": {
        "序号": "sequence_no",
        "人员代码": "employee_code", 
        "姓名": "employee_name",
        "部门名称": "department",
        "基本离休费": "basic_retirement_fee",
        "结余津贴": "surplus_allowance",
        "生活补贴": "living_allowance",
        "住房补贴": "housing_allowance",
        "物业补贴": "property_allowance",
        "离休补贴": "retirement_allowance",
        "护理费": "nursing_fee",
        "增发一次性生活补贴": "additional_living_allowance",
        "补发": "supplementary_payment",
        "合计": "total_amount",
        "借支": "advance_payment",
        "备注": "remarks"
    },
    "retired": {
        "序号": "sequence_no",
        "人员代码": "employee_code",
        "姓名": "employee_name", 
        "部门名称": "department",
        "人员类别代码": "category_code",
        "基本退休费": "basic_pension",
        "津贴": "allowance",
        "结余津贴": "surplus_allowance",
        "离退休生活补贴": "retirement_living_allowance",
        "护理费": "nursing_fee",
        "物业补贴": "property_allowance",
        "住房补贴": "housing_allowance",
        "增资预付": "advance_salary_increase",
        "2016待遇调整": "adjustment_2016",
        "2017待遇调整": "adjustment_2017",
        "2018待遇调整": "adjustment_2018",
        "2019待遇调整": "adjustment_2019",
        "2020待遇调整": "adjustment_2020",
        "2021待遇调整": "adjustment_2021",
        "2022待遇调整": "adjustment_2022",
        "2023待遇调整": "adjustment_2023",
        "补发": "supplementary_payment",
        "借支": "advance_payment",
        "应发工资": "gross_salary",
        "公积": "provident_fund",
        "保险扣款": "insurance_deduction",
        "备注": "remarks"
    }
    # 其他表类型...
}
```

**3. 智能字段适配器**
```python
class IntelligentFieldAdapter:
    def adapt_fields(self, excel_headers: List[str], table_type: str, 
                    db_fields: List[str]) -> Dict[str, str]:
        """智能适配字段映射"""
        # 1. 获取表类型模板
        template = FIELD_MAPPING_TEMPLATES.get(table_type, {})
        
        # 2. 清理Excel表头
        clean_headers = [self._clean_header(h) for h in excel_headers]
        
        # 3. 建立映射关系（数据库字段名 → 显示名）
        field_mapping = {}
        
        # 4. 优先使用模板匹配
        for clean_header in clean_headers:
            if clean_header in template:
                db_field = template[clean_header]
                if db_field in db_fields:
                    field_mapping[db_field] = clean_header
        
        # 5. 处理数据库有但Excel无的字段
        for db_field in db_fields:
            if db_field not in field_mapping:
                # 生成友好的默认显示名
                display_name = self._generate_friendly_name(db_field)
                field_mapping[db_field] = display_name
                
        return field_mapping
    
    def _clean_header(self, header: str) -> str:
        """清理表头特殊字符"""
        import re
        # 移除空格、换行符、制表符等
        cleaned = re.sub(r'\s+', '', header)
        return cleaned.strip()
        
    def _generate_friendly_name(self, db_field: str) -> str:
        """为数据库字段生成友好显示名"""
        # 将下划线转换为空格并标题化
        friendly = db_field.replace('_', ' ').title()
        # 可以添加更多智能转换规则
        return friendly
```

**4. 用户自定义表头管理**
```python
class CustomHeaderManager:
    def show_table_header_customization(self, table_name: str, table_type: str):
        """显示表类型专用的表头自定义对话框"""
        
        # 获取该表类型的所有可用字段
        available_fields = self._get_available_fields_by_type(table_type)
        
        # 获取当前显示的字段
        current_fields = self._get_current_displayed_fields(table_name)
        
        # 创建自定义对话框
        dialog = TableHeaderCustomizationDialog(
            table_name=table_name,
            table_type=table_type,
            available_fields=available_fields,
            current_fields=current_fields,
            parent=self
        )
        
        if dialog.exec_() == QDialog.Accepted:
            selected_fields = dialog.get_selected_fields()
            self._apply_custom_header_preference(table_name, selected_fields)
```

#### 3.3 用户体验优化

**优化1：表头上下文菜单**
```python
# 右键表头时显示上下文菜单
context_menu_items = [
    "编辑显示名称",
    "隐藏此列", 
    "显示/隐藏列...",
    "重置为默认表头",
    f"定制{table_type}表头..."
]
```

**优化2：表头提示信息**
```python
# 表头hover时显示字段信息
tooltip_text = f"""
显示名称: {display_name}
数据库字段: {db_field_name}
表类型: {table_type}
数据类型: {field_data_type}
最后修改: {last_modified}
"""
```

#### 3.4 数据存储结构优化

**新的配置文件结构：**
```json
{
  "version": "3.0",
  "table_type_templates": {
    "retired_old": {
      "display_name": "离休人员工资表",
      "default_fields": ["序号", "姓名", "部门名称", "基本离休费", "合计"],
      "field_mappings": { /* 模板映射 */ }
    }
  },
  "table_instances": {
    "salary_data_2025_05_retired_old": {
      "table_type": "retired_old",
      "custom_field_mappings": { /* 用户自定义映射 */ },
      "display_preferences": {
        "visible_fields": ["序号", "姓名", "合计"],
        "field_order": [0, 1, 2],
        "column_widths": {"序号": 80, "姓名": 120}
      }
    }
  }
}
```

### 📊 四、方案效果预测

#### 4.1 解决问题对照表

| 用户问题 | 当前状况 | 解决方案效果 |
|---------|---------|-------------|
| 表头固定，不符合实际情况 | 所有表显示相同表头 | ✅ 根据表类型动态显示对应表头 |
| 4类表头字段不同无法适配 | 手动配置，易出错 | ✅ 自动识别表类型并应用专用模板 |
| 表头数量不一致处理 | 缺少处理机制 | ✅ 智能适配，自动生成缺失字段显示名 |
| 双击表头修改保存机制 | 保存位置不明确 | ✅ 按表类型分层保存，支持历史记录 |

#### 4.2 用户操作流程对比

**优化前：**
1. 导入Excel → 2. 手动编辑JSON配置 → 3. 重启系统 → 4. 检查效果

**优化后：**
1. 导入Excel（自动识别表类型） → 2. 系统自动应用对应表头 → 3. 可选择性双击编辑 → 4. 立即生效

#### 4.3 预期收益

**技术收益：**
- 表头映射准确率：1.8% → 95%+
- 用户配置复杂度：需要技术背景 → 零技术门槛
- 系统维护成本：高 → 低

**用户体验收益：**
- 操作便捷性：大幅提升
- 功能适用性：覆盖所有表类型
- 错误发生率：显著降低

### 🔧 五、实施建议

#### 5.1 实施优先级

**Phase 1（高优先级）：**
1. 实现表类型识别机制
2. 创建4类工资表的字段映射模板
3. 修改表头显示逻辑，支持动态切换

**Phase 2（中优先级）：**
1. 实现智能字段适配器
2. 优化双击编辑功能
3. 完善配置存储结构

**Phase 3（低优先级）：**
1. 添加表头自定义对话框
2. 实现表头上下文菜单
3. 添加字段提示和帮助功能

#### 5.2 技术风险控制

**风险1：兼容性问题**
- 保留旧配置格式的兼容性读取
- 提供配置迁移工具

**风险2：性能影响**
- 字段映射缓存机制
- 延迟加载非关键配置

**风险3：用户学习成本**
- 提供使用向导
- 准备详细的用户手册

---

## 💡 总结

字段映射问题的根本原因是**缺乏表类型感知能力**，导致系统无法为不同类型的工资表提供对应的表头显示。通过建立"表类型识别 → 专用模板 → 智能适配 → 用户自定义"的完整解决体系，可以根本性地解决用户提出的所有问题，并大幅提升系统的易用性和适用性。

这个方案不仅解决了当前问题，还为未来扩展更多表类型奠定了良好的架构基础。

---

