{"timestamp": "2025-06-27T11:40:14.868722", "table_name": "salary_data_2025_01_retired_employees", "action": "save", "field_count": 27, "fields": ["sequence_number", "employee_id", "employee_name", "department", "employee_type_code", "basic_retirement_salary", "allowance", "balance_allowance", "retirement_living_allowance", "nursing_fee", "property_allowance", "housing_allowance", "salary_advance", "adjustment_2016", "adjustment_2017", "adjustment_2018", "adjustment_2019", "adjustment_2020", "adjustment_2021", "adjustment_2022", "adjustment_2023", "supplement", "advance", "total_salary", "provident_fund", "insurance_deduction", "remarks"]}
{"timestamp": "2025-06-27T11:44:40.291047", "table_name": "salary_data_2025_01_active_employees_test", "action": "save", "field_count": 23, "fields": ["sequence_number", "employee_id", "employee_name", "department", "employee_type_code", "employee_type", "position_salary_2025", "grade_salary_2025", "allowance", "balance_allowance", "basic_performance_2025", "health_fee", "transport_allowance", "property_allowance", "housing_allowance", "car_allowance", "communication_allowance", "performance_bonus_2025", "supplement", "advance", "total_salary", "provident_fund_2025", "pension_insurance"]}
{"timestamp": "2025-06-27T11:48:17.484650", "table_name": "salary_data_2025_01_retired_test", "action": "save", "field_count": 27, "fields": ["sequence_number", "employee_id", "employee_name", "department", "employee_type_code", "basic_retirement_salary", "allowance", "balance_allowance", "retirement_living_allowance", "nursing_fee", "property_allowance", "housing_allowance", "salary_advance", "adjustment_2016", "adjustment_2017", "adjustment_2018", "adjustment_2019", "adjustment_2020", "adjustment_2021", "adjustment_2022", "adjustment_2023", "supplement", "advance", "total_salary", "provident_fund", "insurance_deduction", "remarks"]}
{"timestamp": "2025-06-27T11:48:17.489947", "table_name": "salary_data_2025_01_active_test", "action": "save", "field_count": 23, "fields": ["sequence_number", "employee_id", "employee_name", "department", "employee_type_code", "employee_type", "position_salary_2025", "grade_salary_2025", "allowance", "balance_allowance", "basic_performance_2025", "health_fee", "transport_allowance", "property_allowance", "housing_allowance", "car_allowance", "communication_allowance", "performance_bonus_2025", "supplement", "advance", "total_salary", "provident_fund_2025", "pension_insurance"]}
{"timestamp": "2025-06-27T11:48:17.489947", "table_name": "salary_data_2025_01_a_grade_test", "action": "save", "field_count": 21, "fields": ["sequence_number", "employee_id", "employee_name", "department", "employee_type", "employee_type_code", "position_salary_2025", "seniority_salary_2025", "allowance", "balance_allowance", "basic_performance_2025", "health_fee", "living_allowance_2025", "car_allowance", "performance_bonus_2025", "supplement", "advance", "total_salary", "provident_fund_2025", "insurance_deduction", "pension_insurance"]}
